{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/components/ads/AdSenseScript.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ads/AdSenseScript.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ads/AdSenseScript.tsx <module evaluation>\",\n    \"default\",\n);\nexport const useAdSenseStatus = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAdSenseStatus() from the server but useAdSenseStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ads/AdSenseScript.tsx <module evaluation>\",\n    \"useAdSenseStatus\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sEACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/components/ads/AdSenseScript.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ads/AdSenseScript.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ads/AdSenseScript.tsx\",\n    \"default\",\n);\nexport const useAdSenseStatus = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAdSenseStatus() from the server but useAdSenseStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ads/AdSenseScript.tsx\",\n    \"useAdSenseStatus\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kDACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport AdSenseScript from \"@/components/ads/AdSenseScript\";\nimport \"./globals.css\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nexport const metadata: Metadata = {\n  title: \"Convertidor Universal - Herramientas de Conversión para Estudiantes\",\n  description: \"Convertidor universal gratuito para estudiantes. Convierte monedas, medidas, temperatura, peso, volumen y más. Interfaz limpia y fácil de usar.\",\n  keywords: [\"convertidor\", \"conversión\", \"monedas\", \"medidas\", \"temperatura\", \"estudiantes\", \"herramientas\", \"calculadora\"],\n  authors: [{ name: \"Convertidor Universal\" }],\n  creator: \"Convertidor Universal\",\n  publisher: \"Convertidor Universal\",\n  robots: \"index, follow\",\n  openGraph: {\n    title: \"Convertidor Universal - Herramientas de Conversión\",\n    description: \"Convertidor universal gratuito para estudiantes. Convierte monedas, medidas, temperatura y más.\",\n    type: \"website\",\n    locale: \"es_ES\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Convertidor Universal - Herramientas de Conversión\",\n    description: \"Convertidor universal gratuito para estudiantes.\",\n  },\n  viewport: \"width=device-width, initial-scale=1\",\n  themeColor: \"#3b82f6\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"es\" className={inter.variable}>\n      <head>\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n        <link rel=\"apple-touch-icon\" href=\"/apple-touch-icon.png\" />\n        <meta name=\"theme-color\" content=\"#3b82f6\" />\n      </head>\n      <body className=\"font-sans antialiased bg-gray-50 text-gray-900\">\n        <AdSenseScript />\n        <div className=\"min-h-screen flex flex-col\">\n          {children}\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;;;AAQO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAe;QAAc;QAAW;QAAW;QAAe;QAAe;QAAgB;KAAc;IAC1H,SAAS;QAAC;YAAE,MAAM;QAAwB;KAAE;IAC5C,SAAS;IACT,WAAW;IACX,QAAQ;IACR,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA,UAAU;IACV,YAAY;AACd;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,yIAAA,CAAA,UAAK,CAAC,QAAQ;;0BACvC,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAEnC,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,0IAAA,CAAA,UAAa;;;;;kCACd,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}