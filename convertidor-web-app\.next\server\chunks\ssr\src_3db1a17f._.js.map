{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/store/useStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { ConversionResult, UserPreferences, ExchangeRate } from '@/types';\n\ninterface AppState {\n  // Preferencias del usuario\n  preferences: UserPreferences;\n  setPreferences: (preferences: Partial<UserPreferences>) => void;\n  \n  // Historial de conversiones\n  conversionHistory: ConversionResult[];\n  addConversion: (conversion: ConversionResult) => void;\n  clearHistory: () => void;\n  \n  // Tasas de cambio\n  exchangeRates: ExchangeRate | null;\n  setExchangeRates: (rates: ExchangeRate) => void;\n  \n  // Estado de búsqueda\n  searchQuery: string;\n  setSearchQuery: (query: string) => void;\n  \n  // Estado de carga\n  isLoading: boolean;\n  setIsLoading: (loading: boolean) => void;\n  \n  // Errores\n  error: string | null;\n  setError: (error: string | null) => void;\n}\n\nconst defaultPreferences: UserPreferences = {\n  defaultSystem: 'metric',\n  decimalPlaces: 2,\n  theme: 'system',\n  language: 'es',\n  showHistory: true,\n  maxHistoryItems: 50,\n};\n\nexport const useStore = create<AppState>()(\n  persist(\n    (set, get) => ({\n      // Estado inicial\n      preferences: defaultPreferences,\n      conversionHistory: [],\n      exchangeRates: null,\n      searchQuery: '',\n      isLoading: false,\n      error: null,\n      \n      // Acciones para preferencias\n      setPreferences: (newPreferences) =>\n        set((state) => ({\n          preferences: { ...state.preferences, ...newPreferences },\n        })),\n      \n      // Acciones para historial\n      addConversion: (conversion) =>\n        set((state) => {\n          const newHistory = [conversion, ...state.conversionHistory];\n          const maxItems = state.preferences.maxHistoryItems;\n          return {\n            conversionHistory: newHistory.slice(0, maxItems),\n          };\n        }),\n      \n      clearHistory: () => set({ conversionHistory: [] }),\n      \n      // Acciones para tasas de cambio\n      setExchangeRates: (rates) => set({ exchangeRates: rates }),\n      \n      // Acciones para búsqueda\n      setSearchQuery: (query) => set({ searchQuery: query }),\n      \n      // Acciones para estado de carga\n      setIsLoading: (loading) => set({ isLoading: loading }),\n      \n      // Acciones para errores\n      setError: (error) => set({ error }),\n    }),\n    {\n      name: 'convertidor-storage',\n      partialize: (state) => ({\n        preferences: state.preferences,\n        conversionHistory: state.conversionHistory,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA8BA,MAAM,qBAAsC;IAC1C,eAAe;IACf,eAAe;IACf,OAAO;IACP,UAAU;IACV,aAAa;IACb,iBAAiB;AACnB;AAEO,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC3B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,iBAAiB;QACjB,aAAa;QACb,mBAAmB,EAAE;QACrB,eAAe;QACf,aAAa;QACb,WAAW;QACX,OAAO;QAEP,6BAA6B;QAC7B,gBAAgB,CAAC,iBACf,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBAAE,GAAG,MAAM,WAAW;wBAAE,GAAG,cAAc;oBAAC;gBACzD,CAAC;QAEH,0BAA0B;QAC1B,eAAe,CAAC,aACd,IAAI,CAAC;gBACH,MAAM,aAAa;oBAAC;uBAAe,MAAM,iBAAiB;iBAAC;gBAC3D,MAAM,WAAW,MAAM,WAAW,CAAC,eAAe;gBAClD,OAAO;oBACL,mBAAmB,WAAW,KAAK,CAAC,GAAG;gBACzC;YACF;QAEF,cAAc,IAAM,IAAI;gBAAE,mBAAmB,EAAE;YAAC;QAEhD,gCAAgC;QAChC,kBAAkB,CAAC,QAAU,IAAI;gBAAE,eAAe;YAAM;QAExD,yBAAyB;QACzB,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QAEpD,gCAAgC;QAChC,cAAc,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAEpD,wBAAwB;QACxB,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;IACnC,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,mBAAmB,MAAM,iBAAiB;QAC5C,CAAC;AACH", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/data/conversions.ts"], "sourcesContent": ["import { ConversionCategory, Currency } from '@/types';\n\n// Categorías de conversión\nexport const conversionCategories: ConversionCategory[] = [\n  {\n    id: 'length',\n    name: 'Longitud',\n    description: 'Convertir entre diferentes unidades de longitud',\n    icon: 'Ruler',\n    baseUnit: 'meter',\n    units: [\n      { id: 'meter', name: 'Metro', symbol: 'm', factor: 1 },\n      { id: 'kilometer', name: 'Kilómetro', symbol: 'km', factor: 1000 },\n      { id: 'centimeter', name: 'Centímetro', symbol: 'cm', factor: 0.01 },\n      { id: 'millimeter', name: 'Milímet<PERSON>', symbol: 'mm', factor: 0.001 },\n      { id: 'inch', name: '<PERSON><PERSON>ga<PERSON>', symbol: 'in', factor: 0.0254 },\n      { id: 'foot', name: 'Pie', symbol: 'ft', factor: 0.3048 },\n      { id: 'yard', name: '<PERSON><PERSON>', symbol: 'yd', factor: 0.9144 },\n      { id: 'mile', name: '<PERSON><PERSON>', symbol: 'mi', factor: 1609.344 },\n    ],\n  },\n  {\n    id: 'area',\n    name: 'Superficie',\n    description: 'Convertir entre diferentes unidades de superficie',\n    icon: 'Square',\n    baseUnit: 'square_meter',\n    units: [\n      { id: 'square_meter', name: 'Metro cuadrado', symbol: 'm²', factor: 1 },\n      { id: 'square_kilometer', name: 'Kilómetro cuadrado', symbol: 'km²', factor: 1000000 },\n      { id: 'hectare', name: 'Hectárea', symbol: 'ha', factor: 10000 },\n      { id: 'acre', name: 'Acre', symbol: 'ac', factor: 4046.8564224 },\n      { id: 'square_foot', name: 'Pie cuadrado', symbol: 'ft²', factor: 0.09290304 },\n      { id: 'square_yard', name: 'Yarda cuadrada', symbol: 'yd²', factor: 0.83612736 },\n    ],\n  },\n  {\n    id: 'volume',\n    name: 'Volumen',\n    description: 'Convertir entre diferentes unidades de volumen',\n    icon: 'Box',\n    baseUnit: 'liter',\n    units: [\n      { id: 'liter', name: 'Litro', symbol: 'L', factor: 1 },\n      { id: 'milliliter', name: 'Mililitro', symbol: 'mL', factor: 0.001 },\n      { id: 'cubic_meter', name: 'Metro cúbico', symbol: 'm³', factor: 1000 },\n      { id: 'gallon_us', name: 'Galón (US)', symbol: 'gal', factor: 3.785411784 },\n      { id: 'gallon_uk', name: 'Galón (UK)', symbol: 'gal', factor: 4.54609 },\n      { id: 'fluid_ounce_us', name: 'Onza líquida (US)', symbol: 'fl oz', factor: 0.0295735296875 },\n      { id: 'fluid_ounce_uk', name: 'Onza líquida (UK)', symbol: 'fl oz', factor: 0.0284130625 },\n      { id: 'tablespoon', name: 'Cucharada', symbol: 'tbsp', factor: 0.01478676478125 },\n      { id: 'teaspoon', name: 'Cucharadita', symbol: 'tsp', factor: 0.00492892159375 },\n    ],\n  },\n  {\n    id: 'weight',\n    name: 'Peso y Masa',\n    description: 'Convertir entre diferentes unidades de peso y masa',\n    icon: 'Weight',\n    baseUnit: 'kilogram',\n    units: [\n      { id: 'kilogram', name: 'Kilogramo', symbol: 'kg', factor: 1 },\n      { id: 'gram', name: 'Gramo', symbol: 'g', factor: 0.001 },\n      { id: 'milligram', name: 'Miligramo', symbol: 'mg', factor: 0.000001 },\n      { id: 'metric_ton', name: 'Tonelada métrica', symbol: 't', factor: 1000 },\n      { id: 'pound', name: 'Libra', symbol: 'lb', factor: 0.45359237 },\n      { id: 'ounce', name: 'Onza', symbol: 'oz', factor: 0.028349523125 },\n      { id: 'stone', name: 'Piedra', symbol: 'st', factor: 6.35029318 },\n    ],\n  },\n  {\n    id: 'temperature',\n    name: 'Temperatura',\n    description: 'Convertir entre diferentes escalas de temperatura',\n    icon: 'Thermometer',\n    baseUnit: 'celsius',\n    units: [\n      { id: 'celsius', name: 'Celsius', symbol: '°C', factor: 1 },\n      { id: 'fahrenheit', name: 'Fahrenheit', symbol: '°F', factor: 1 },\n      { id: 'kelvin', name: 'Kelvin', symbol: 'K', factor: 1 },\n    ],\n  },\n  {\n    id: 'time',\n    name: 'Tiempo',\n    description: 'Convertir entre diferentes unidades de tiempo',\n    icon: 'Clock',\n    baseUnit: 'second',\n    units: [\n      { id: 'second', name: 'Segundo', symbol: 's', factor: 1 },\n      { id: 'minute', name: 'Minuto', symbol: 'min', factor: 60 },\n      { id: 'hour', name: 'Hora', symbol: 'h', factor: 3600 },\n      { id: 'day', name: 'Día', symbol: 'd', factor: 86400 },\n      { id: 'week', name: 'Semana', symbol: 'sem', factor: 604800 },\n      { id: 'month', name: 'Mes', symbol: 'mes', factor: 2629746 },\n      { id: 'year', name: 'Año', symbol: 'año', factor: 31556952 },\n    ],\n  },\n  {\n    id: 'speed',\n    name: 'Velocidad',\n    description: 'Convertir entre diferentes unidades de velocidad',\n    icon: 'Gauge',\n    baseUnit: 'meter_per_second',\n    units: [\n      { id: 'meter_per_second', name: 'Metro por segundo', symbol: 'm/s', factor: 1 },\n      { id: 'kilometer_per_hour', name: 'Kilómetro por hora', symbol: 'km/h', factor: 0.277778 },\n      { id: 'mile_per_hour', name: 'Milla por hora', symbol: 'mph', factor: 0.44704 },\n      { id: 'knot', name: 'Nudo', symbol: 'kn', factor: 0.514444 },\n      { id: 'mach', name: 'Mach', symbol: 'M', factor: 343 },\n    ],\n  },\n  {\n    id: 'digital',\n    name: 'Datos Digitales',\n    description: 'Convertir entre diferentes unidades de almacenamiento digital',\n    icon: 'HardDrive',\n    baseUnit: 'byte',\n    units: [\n      { id: 'bit', name: 'Bit', symbol: 'bit', factor: 0.125 },\n      { id: 'byte', name: 'Byte', symbol: 'B', factor: 1 },\n      { id: 'kilobyte', name: 'Kilobyte', symbol: 'KB', factor: 1024 },\n      { id: 'megabyte', name: 'Megabyte', symbol: 'MB', factor: 1048576 },\n      { id: 'gigabyte', name: 'Gigabyte', symbol: 'GB', factor: 1073741824 },\n      { id: 'terabyte', name: 'Terabyte', symbol: 'TB', factor: 1099511627776 },\n      { id: 'petabyte', name: 'Petabyte', symbol: 'PB', factor: 1125899906842624 },\n    ],\n  },\n  {\n    id: 'energy',\n    name: 'Energía',\n    description: 'Convertir entre diferentes unidades de energía',\n    icon: 'Zap',\n    baseUnit: 'joule',\n    units: [\n      { id: 'joule', name: 'Julio', symbol: 'J', factor: 1 },\n      { id: 'kilojoule', name: 'Kilojulio', symbol: 'kJ', factor: 1000 },\n      { id: 'calorie', name: 'Caloría (dietética)', symbol: 'cal', factor: 4184 },\n      { id: 'calorie_thermo', name: 'Caloría (termodinámica)', symbol: 'cal', factor: 4.184 },\n      { id: 'kilowatt_hour', name: 'Kilovatio-hora', symbol: 'kWh', factor: 3600000 },\n    ],\n  },\n  {\n    id: 'pressure',\n    name: 'Presión',\n    description: 'Convertir entre diferentes unidades de presión',\n    icon: 'Gauge',\n    baseUnit: 'pascal',\n    units: [\n      { id: 'pascal', name: 'Pascal', symbol: 'Pa', factor: 1 },\n      { id: 'kilopascal', name: 'Kilopascal', symbol: 'kPa', factor: 1000 },\n      { id: 'psi', name: 'Libra por pulgada cuadrada', symbol: 'psi', factor: 6894.757 },\n      { id: 'atmosphere', name: 'Atmósfera', symbol: 'atm', factor: 101325 },\n      { id: 'bar', name: 'Bar', symbol: 'bar', factor: 100000 },\n    ],\n  },\n];\n\n// Monedas principales\nexport const currencies: Currency[] = [\n  { code: 'USD', name: 'Dólar estadounidense', symbol: '$', flag: '🇺🇸' },\n  { code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺' },\n  { code: 'GBP', name: 'Libra esterlina', symbol: '£', flag: '🇬🇧' },\n  { code: 'JPY', name: 'Yen japonés', symbol: '¥', flag: '🇯🇵' },\n  { code: 'CAD', name: 'Dólar canadiense', symbol: 'C$', flag: '🇨🇦' },\n  { code: 'AUD', name: 'Dólar australiano', symbol: 'A$', flag: '🇦🇺' },\n  { code: 'MXN', name: 'Peso mexicano', symbol: '$', flag: '🇲🇽' },\n  { code: 'CRC', name: 'Colón costarricense', symbol: '₡', flag: '🇨🇷' },\n  { code: 'GTQ', name: 'Quetzal guatemalteco', symbol: 'Q', flag: '🇬🇹' },\n  { code: 'HNL', name: 'Lempira hondureño', symbol: 'L', flag: '🇭🇳' },\n  { code: 'NIO', name: 'Córdoba nicaragüense', symbol: 'C$', flag: '🇳🇮' },\n  { code: 'PAB', name: 'Balboa panameño', symbol: 'B/.', flag: '🇵🇦' },\n  { code: 'DOP', name: 'Peso dominicano', symbol: 'RD$', flag: '🇩🇴' },\n  { code: 'COP', name: 'Peso colombiano', symbol: '$', flag: '🇨🇴' },\n  { code: 'PEN', name: 'Sol peruano', symbol: 'S/', flag: '🇵🇪' },\n  { code: 'CLP', name: 'Peso chileno', symbol: '$', flag: '🇨🇱' },\n  { code: 'ARS', name: 'Peso argentino', symbol: '$', flag: '🇦🇷' },\n  { code: 'BRL', name: 'Real brasileño', symbol: 'R$', flag: '🇧🇷' },\n];\n"], "names": [], "mappings": ";;;;AAGO,MAAM,uBAA6C;IACxD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAS,MAAM;gBAAS,QAAQ;gBAAK,QAAQ;YAAE;YACrD;gBAAE,IAAI;gBAAa,MAAM;gBAAa,QAAQ;gBAAM,QAAQ;YAAK;YACjE;gBAAE,IAAI;gBAAc,MAAM;gBAAc,QAAQ;gBAAM,QAAQ;YAAK;YACnE;gBAAE,IAAI;gBAAc,MAAM;gBAAa,QAAQ;gBAAM,QAAQ;YAAM;YACnE;gBAAE,IAAI;gBAAQ,MAAM;gBAAW,QAAQ;gBAAM,QAAQ;YAAO;YAC5D;gBAAE,IAAI;gBAAQ,MAAM;gBAAO,QAAQ;gBAAM,QAAQ;YAAO;YACxD;gBAAE,IAAI;gBAAQ,MAAM;gBAAS,QAAQ;gBAAM,QAAQ;YAAO;YAC1D;gBAAE,IAAI;gBAAQ,MAAM;gBAAS,QAAQ;gBAAM,QAAQ;YAAS;SAC7D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAgB,MAAM;gBAAkB,QAAQ;gBAAM,QAAQ;YAAE;YACtE;gBAAE,IAAI;gBAAoB,MAAM;gBAAsB,QAAQ;gBAAO,QAAQ;YAAQ;YACrF;gBAAE,IAAI;gBAAW,MAAM;gBAAY,QAAQ;gBAAM,QAAQ;YAAM;YAC/D;gBAAE,IAAI;gBAAQ,MAAM;gBAAQ,QAAQ;gBAAM,QAAQ;YAAa;YAC/D;gBAAE,IAAI;gBAAe,MAAM;gBAAgB,QAAQ;gBAAO,QAAQ;YAAW;YAC7E;gBAAE,IAAI;gBAAe,MAAM;gBAAkB,QAAQ;gBAAO,QAAQ;YAAW;SAChF;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAS,MAAM;gBAAS,QAAQ;gBAAK,QAAQ;YAAE;YACrD;gBAAE,IAAI;gBAAc,MAAM;gBAAa,QAAQ;gBAAM,QAAQ;YAAM;YACnE;gBAAE,IAAI;gBAAe,MAAM;gBAAgB,QAAQ;gBAAM,QAAQ;YAAK;YACtE;gBAAE,IAAI;gBAAa,MAAM;gBAAc,QAAQ;gBAAO,QAAQ;YAAY;YAC1E;gBAAE,IAAI;gBAAa,MAAM;gBAAc,QAAQ;gBAAO,QAAQ;YAAQ;YACtE;gBAAE,IAAI;gBAAkB,MAAM;gBAAqB,QAAQ;gBAAS,QAAQ;YAAgB;YAC5F;gBAAE,IAAI;gBAAkB,MAAM;gBAAqB,QAAQ;gBAAS,QAAQ;YAAa;YACzF;gBAAE,IAAI;gBAAc,MAAM;gBAAa,QAAQ;gBAAQ,QAAQ;YAAiB;YAChF;gBAAE,IAAI;gBAAY,MAAM;gBAAe,QAAQ;gBAAO,QAAQ;YAAiB;SAChF;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAY,MAAM;gBAAa,QAAQ;gBAAM,QAAQ;YAAE;YAC7D;gBAAE,IAAI;gBAAQ,MAAM;gBAAS,QAAQ;gBAAK,QAAQ;YAAM;YACxD;gBAAE,IAAI;gBAAa,MAAM;gBAAa,QAAQ;gBAAM,QAAQ;YAAS;YACrE;gBAAE,IAAI;gBAAc,MAAM;gBAAoB,QAAQ;gBAAK,QAAQ;YAAK;YACxE;gBAAE,IAAI;gBAAS,MAAM;gBAAS,QAAQ;gBAAM,QAAQ;YAAW;YAC/D;gBAAE,IAAI;gBAAS,MAAM;gBAAQ,QAAQ;gBAAM,QAAQ;YAAe;YAClE;gBAAE,IAAI;gBAAS,MAAM;gBAAU,QAAQ;gBAAM,QAAQ;YAAW;SACjE;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAW,MAAM;gBAAW,QAAQ;gBAAM,QAAQ;YAAE;YAC1D;gBAAE,IAAI;gBAAc,MAAM;gBAAc,QAAQ;gBAAM,QAAQ;YAAE;YAChE;gBAAE,IAAI;gBAAU,MAAM;gBAAU,QAAQ;gBAAK,QAAQ;YAAE;SACxD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAU,MAAM;gBAAW,QAAQ;gBAAK,QAAQ;YAAE;YACxD;gBAAE,IAAI;gBAAU,MAAM;gBAAU,QAAQ;gBAAO,QAAQ;YAAG;YAC1D;gBAAE,IAAI;gBAAQ,MAAM;gBAAQ,QAAQ;gBAAK,QAAQ;YAAK;YACtD;gBAAE,IAAI;gBAAO,MAAM;gBAAO,QAAQ;gBAAK,QAAQ;YAAM;YACrD;gBAAE,IAAI;gBAAQ,MAAM;gBAAU,QAAQ;gBAAO,QAAQ;YAAO;YAC5D;gBAAE,IAAI;gBAAS,MAAM;gBAAO,QAAQ;gBAAO,QAAQ;YAAQ;YAC3D;gBAAE,IAAI;gBAAQ,MAAM;gBAAO,QAAQ;gBAAO,QAAQ;YAAS;SAC5D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAoB,MAAM;gBAAqB,QAAQ;gBAAO,QAAQ;YAAE;YAC9E;gBAAE,IAAI;gBAAsB,MAAM;gBAAsB,QAAQ;gBAAQ,QAAQ;YAAS;YACzF;gBAAE,IAAI;gBAAiB,MAAM;gBAAkB,QAAQ;gBAAO,QAAQ;YAAQ;YAC9E;gBAAE,IAAI;gBAAQ,MAAM;gBAAQ,QAAQ;gBAAM,QAAQ;YAAS;YAC3D;gBAAE,IAAI;gBAAQ,MAAM;gBAAQ,QAAQ;gBAAK,QAAQ;YAAI;SACtD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAO,MAAM;gBAAO,QAAQ;gBAAO,QAAQ;YAAM;YACvD;gBAAE,IAAI;gBAAQ,MAAM;gBAAQ,QAAQ;gBAAK,QAAQ;YAAE;YACnD;gBAAE,IAAI;gBAAY,MAAM;gBAAY,QAAQ;gBAAM,QAAQ;YAAK;YAC/D;gBAAE,IAAI;gBAAY,MAAM;gBAAY,QAAQ;gBAAM,QAAQ;YAAQ;YAClE;gBAAE,IAAI;gBAAY,MAAM;gBAAY,QAAQ;gBAAM,QAAQ;YAAW;YACrE;gBAAE,IAAI;gBAAY,MAAM;gBAAY,QAAQ;gBAAM,QAAQ;YAAc;YACxE;gBAAE,IAAI;gBAAY,MAAM;gBAAY,QAAQ;gBAAM,QAAQ;YAAiB;SAC5E;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAS,MAAM;gBAAS,QAAQ;gBAAK,QAAQ;YAAE;YACrD;gBAAE,IAAI;gBAAa,MAAM;gBAAa,QAAQ;gBAAM,QAAQ;YAAK;YACjE;gBAAE,IAAI;gBAAW,MAAM;gBAAuB,QAAQ;gBAAO,QAAQ;YAAK;YAC1E;gBAAE,IAAI;gBAAkB,MAAM;gBAA2B,QAAQ;gBAAO,QAAQ;YAAM;YACtF;gBAAE,IAAI;gBAAiB,MAAM;gBAAkB,QAAQ;gBAAO,QAAQ;YAAQ;SAC/E;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBAAE,IAAI;gBAAU,MAAM;gBAAU,QAAQ;gBAAM,QAAQ;YAAE;YACxD;gBAAE,IAAI;gBAAc,MAAM;gBAAc,QAAQ;gBAAO,QAAQ;YAAK;YACpE;gBAAE,IAAI;gBAAO,MAAM;gBAA8B,QAAQ;gBAAO,QAAQ;YAAS;YACjF;gBAAE,IAAI;gBAAc,MAAM;gBAAa,QAAQ;gBAAO,QAAQ;YAAO;YACrE;gBAAE,IAAI;gBAAO,MAAM;gBAAO,QAAQ;gBAAO,QAAQ;YAAO;SACzD;IACH;CACD;AAGM,MAAM,aAAyB;IACpC;QAAE,MAAM;QAAO,MAAM;QAAwB,QAAQ;QAAK,MAAM;IAAO;IACvE;QAAE,MAAM;QAAO,MAAM;QAAQ,QAAQ;QAAK,MAAM;IAAO;IACvD;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAK,MAAM;IAAO;IAClE;QAAE,MAAM;QAAO,MAAM;QAAe,QAAQ;QAAK,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAO,MAAM;QAAoB,QAAQ;QAAM,MAAM;IAAO;IACpE;QAAE,MAAM;QAAO,MAAM;QAAqB,QAAQ;QAAM,MAAM;IAAO;IACrE;QAAE,MAAM;QAAO,MAAM;QAAiB,QAAQ;QAAK,MAAM;IAAO;IAChE;QAAE,MAAM;QAAO,MAAM;QAAuB,QAAQ;QAAK,MAAM;IAAO;IACtE;QAAE,MAAM;QAAO,MAAM;QAAwB,QAAQ;QAAK,MAAM;IAAO;IACvE;QAAE,MAAM;QAAO,MAAM;QAAqB,QAAQ;QAAK,MAAM;IAAO;IACpE;QAAE,MAAM;QAAO,MAAM;QAAwB,QAAQ;QAAM,MAAM;IAAO;IACxE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAO,MAAM;IAAO;IACpE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAO,MAAM;IAAO;IACpE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAK,MAAM;IAAO;IAClE;QAAE,MAAM;QAAO,MAAM;QAAe,QAAQ;QAAM,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAO,MAAM;QAAgB,QAAQ;QAAK,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAO,MAAM;QAAkB,QAAQ;QAAK,MAAM;IAAO;IACjE;QAAE,MAAM;QAAO,MAAM;QAAkB,QAAQ;QAAM,MAAM;IAAO;CACnE", "debugId": null}}, {"offset": {"line": 664, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/services/searchService.ts"], "sourcesContent": ["import { SearchResult } from '@/types';\nimport { conversionCategories } from '@/data/conversions';\nimport { currencies } from '@/data/conversions';\n\nclass SearchService {\n  private searchIndex: SearchResult[] = [];\n\n  constructor() {\n    this.buildSearchIndex();\n  }\n\n  private buildSearchIndex() {\n    // Agregar convertidores de categorías\n    this.searchIndex = conversionCategories.map(category => ({\n      id: category.id,\n      title: `Convertidor de ${category.name}`,\n      description: category.description,\n      category: 'Convertidor',\n      path: `/${this.getPageSlug(category.id)}`,\n      keywords: [\n        category.name.toLowerCase(),\n        ...category.units.map(unit => unit.name.toLowerCase()),\n        ...category.units.map(unit => unit.symbol.toLowerCase()),\n        'convertir',\n        'conversión',\n        'calculadora'\n      ]\n    }));\n\n    // Agregar convertidor de monedas\n    this.searchIndex.push({\n      id: 'currency',\n      title: 'Convertido<PERSON> de Monedas',\n      description: 'Convierte entre más de 18 monedas internacionales con tasas en tiempo real',\n      category: 'Convertidor',\n      path: '/monedas',\n      keywords: [\n        'monedas',\n        'divisas',\n        'cambio',\n        'dólar',\n        'euro',\n        'peso',\n        'real',\n        'libra',\n        'yen',\n        ...currencies.map(currency => currency.name.toLowerCase()),\n        ...currencies.map(currency => currency.code.toLowerCase()),\n        'tasa de cambio',\n        'convertir monedas'\n      ]\n    });\n\n    // Agregar búsquedas específicas comunes\n    const commonSearches = [\n      {\n        id: 'metros-pies',\n        title: 'Convertir metros a pies',\n        description: 'Conversión rápida de metros a pies',\n        category: 'Conversión específica',\n        path: '/longitud',\n        keywords: ['metros a pies', 'metros pies', 'm a ft', 'meter to feet']\n      },\n      {\n        id: 'celsius-fahrenheit',\n        title: 'Convertir Celsius a Fahrenheit',\n        description: 'Conversión de temperatura Celsius a Fahrenheit',\n        category: 'Conversión específica',\n        path: '/temperatura',\n        keywords: ['celsius fahrenheit', 'c a f', '°c a °f', 'grados celsius fahrenheit']\n      },\n      {\n        id: 'kg-libras',\n        title: 'Convertir kilogramos a libras',\n        description: 'Conversión de peso kilogramos a libras',\n        category: 'Conversión específica',\n        path: '/peso',\n        keywords: ['kilogramos libras', 'kg a lb', 'kilos libras', 'peso']\n      },\n      {\n        id: 'litros-galones',\n        title: 'Convertir litros a galones',\n        description: 'Conversión de volumen litros a galones',\n        category: 'Conversión específica',\n        path: '/volumen',\n        keywords: ['litros galones', 'l a gal', 'volumen']\n      },\n      {\n        id: 'usd-eur',\n        title: 'Convertir USD a EUR',\n        description: 'Conversión de dólares a euros',\n        category: 'Conversión específica',\n        path: '/monedas',\n        keywords: ['usd eur', 'dolar euro', 'dolares euros', 'usd a eur']\n      },\n      {\n        id: 'gb-mb',\n        title: 'Convertir GB a MB',\n        description: 'Conversión de gigabytes a megabytes',\n        category: 'Conversión específica',\n        path: '/datos-digitales',\n        keywords: ['gb mb', 'gigabytes megabytes', 'gb a mb', 'almacenamiento']\n      }\n    ];\n\n    this.searchIndex.push(...commonSearches);\n  }\n\n  private getPageSlug(categoryId: string): string {\n    const slugMap: Record<string, string> = {\n      'length': 'longitud',\n      'area': 'superficie',\n      'volume': 'volumen',\n      'weight': 'peso',\n      'temperature': 'temperatura',\n      'time': 'tiempo',\n      'speed': 'velocidad',\n      'digital': 'datos-digitales',\n      'energy': 'energia',\n      'pressure': 'presion',\n    };\n    return slugMap[categoryId] || categoryId;\n  }\n\n  search(query: string, limit: number = 10): SearchResult[] {\n    if (!query || query.trim().length < 2) {\n      return [];\n    }\n\n    const normalizedQuery = query.toLowerCase().trim();\n    const words = normalizedQuery.split(/\\s+/);\n\n    const results = this.searchIndex\n      .map(item => {\n        let score = 0;\n\n        // Búsqueda exacta en título (puntuación alta)\n        if (item.title.toLowerCase().includes(normalizedQuery)) {\n          score += 100;\n        }\n\n        // Búsqueda exacta en descripción\n        if (item.description.toLowerCase().includes(normalizedQuery)) {\n          score += 50;\n        }\n\n        // Búsqueda en palabras clave\n        const keywordMatches = item.keywords.filter(keyword => \n          keyword.includes(normalizedQuery) || \n          words.some(word => keyword.includes(word))\n        );\n        score += keywordMatches.length * 20;\n\n        // Búsqueda por palabras individuales en título\n        words.forEach(word => {\n          if (word.length > 2 && item.title.toLowerCase().includes(word)) {\n            score += 30;\n          }\n        });\n\n        // Búsqueda por palabras individuales en descripción\n        words.forEach(word => {\n          if (word.length > 2 && item.description.toLowerCase().includes(word)) {\n            score += 15;\n          }\n        });\n\n        return { ...item, score };\n      })\n      .filter(item => item.score > 0)\n      .sort((a, b) => b.score - a.score)\n      .slice(0, limit);\n\n    return results.map(({ score, ...item }) => item);\n  }\n\n  getPopularSearches(): SearchResult[] {\n    return [\n      this.searchIndex.find(item => item.id === 'currency')!,\n      this.searchIndex.find(item => item.id === 'length')!,\n      this.searchIndex.find(item => item.id === 'weight')!,\n      this.searchIndex.find(item => item.id === 'temperature')!,\n      this.searchIndex.find(item => item.id === 'volume')!,\n    ].filter(Boolean);\n  }\n\n  getAllCategories(): string[] {\n    return [...new Set(this.searchIndex.map(item => item.category))];\n  }\n\n  searchByCategory(category: string): SearchResult[] {\n    return this.searchIndex.filter(item => item.category === category);\n  }\n}\n\nexport const searchService = new SearchService();\n"], "names": [], "mappings": ";;;AACA;;;AAGA,MAAM;IACI,cAA8B,EAAE,CAAC;IAEzC,aAAc;QACZ,IAAI,CAAC,gBAAgB;IACvB;IAEQ,mBAAmB;QACzB,sCAAsC;QACtC,IAAI,CAAC,WAAW,GAAG,0HAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,CAAA,WAAY,CAAC;gBACvD,IAAI,SAAS,EAAE;gBACf,OAAO,CAAC,eAAe,EAAE,SAAS,IAAI,EAAE;gBACxC,aAAa,SAAS,WAAW;gBACjC,UAAU;gBACV,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG;gBACzC,UAAU;oBACR,SAAS,IAAI,CAAC,WAAW;uBACtB,SAAS,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,WAAW;uBAChD,SAAS,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,WAAW;oBACrD;oBACA;oBACA;iBACD;YACH,CAAC;QAED,iCAAiC;QACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACpB,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,MAAM;YACN,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;mBACG,0HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,IAAI,CAAC,WAAW;mBACpD,0HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,IAAI,CAAC,WAAW;gBACvD;gBACA;aACD;QACH;QAEA,wCAAwC;QACxC,MAAM,iBAAiB;YACrB;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,UAAU;oBAAC;oBAAiB;oBAAe;oBAAU;iBAAgB;YACvE;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,UAAU;oBAAC;oBAAsB;oBAAS;oBAAW;iBAA4B;YACnF;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,UAAU;oBAAC;oBAAqB;oBAAW;oBAAgB;iBAAO;YACpE;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,UAAU;oBAAC;oBAAkB;oBAAW;iBAAU;YACpD;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,UAAU;oBAAC;oBAAW;oBAAc;oBAAiB;iBAAY;YACnE;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,UAAU;oBAAC;oBAAS;oBAAuB;oBAAW;iBAAiB;YACzE;SACD;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;IAC3B;IAEQ,YAAY,UAAkB,EAAU;QAC9C,MAAM,UAAkC;YACtC,UAAU;YACV,QAAQ;YACR,UAAU;YACV,UAAU;YACV,eAAe;YACf,QAAQ;YACR,SAAS;YACT,WAAW;YACX,UAAU;YACV,YAAY;QACd;QACA,OAAO,OAAO,CAAC,WAAW,IAAI;IAChC;IAEA,OAAO,KAAa,EAAE,QAAgB,EAAE,EAAkB;QACxD,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YACrC,OAAO,EAAE;QACX;QAEA,MAAM,kBAAkB,MAAM,WAAW,GAAG,IAAI;QAChD,MAAM,QAAQ,gBAAgB,KAAK,CAAC;QAEpC,MAAM,UAAU,IAAI,CAAC,WAAW,CAC7B,GAAG,CAAC,CAAA;YACH,IAAI,QAAQ;YAEZ,8CAA8C;YAC9C,IAAI,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,kBAAkB;gBACtD,SAAS;YACX;YAEA,iCAAiC;YACjC,IAAI,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,kBAAkB;gBAC5D,SAAS;YACX;YAEA,6BAA6B;YAC7B,MAAM,iBAAiB,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,UAC1C,QAAQ,QAAQ,CAAC,oBACjB,MAAM,IAAI,CAAC,CAAA,OAAQ,QAAQ,QAAQ,CAAC;YAEtC,SAAS,eAAe,MAAM,GAAG;YAEjC,+CAA+C;YAC/C,MAAM,OAAO,CAAC,CAAA;gBACZ,IAAI,KAAK,MAAM,GAAG,KAAK,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO;oBAC9D,SAAS;gBACX;YACF;YAEA,oDAAoD;YACpD,MAAM,OAAO,CAAC,CAAA;gBACZ,IAAI,KAAK,MAAM,GAAG,KAAK,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO;oBACpE,SAAS;gBACX;YACF;YAEA,OAAO;gBAAE,GAAG,IAAI;gBAAE;YAAM;QAC1B,GACC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG,GAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;QAEZ,OAAO,QAAQ,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,MAAM,GAAK;IAC7C;IAEA,qBAAqC;QACnC,OAAO;YACL,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;SAC3C,CAAC,MAAM,CAAC;IACX;IAEA,mBAA6B;QAC3B,OAAO;eAAI,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;SAAG;IAClE;IAEA,iBAAiB,QAAgB,EAAkB;QACjD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAC3D;AACF;AAEO,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Función para formatear números\nexport function formatNumber(num: number, decimals: number = 2): string {\n  if (isNaN(num)) return '0';\n  \n  // Si el número es muy pequeño, usar notación científica\n  if (Math.abs(num) < 0.0001 && num !== 0) {\n    return num.toExponential(2);\n  }\n  \n  // Si el número es muy grande, usar notación científica\n  if (Math.abs(num) > 1e12) {\n    return num.toExponential(2);\n  }\n  \n  return num.toLocaleString('es-ES', {\n    minimumFractionDigits: 0,\n    maximumFractionDigits: decimals,\n  });\n}\n\n// Función para validar entrada numérica\nexport function isValidNumber(value: string): boolean {\n  if (!value || value.trim() === '') return false;\n  const num = parseFloat(value.replace(/,/g, ''));\n  return !isNaN(num) && isFinite(num);\n}\n\n// Función para parsear número desde string\nexport function parseNumber(value: string): number {\n  if (!value || value.trim() === '') return 0;\n  return parseFloat(value.replace(/,/g, ''));\n}\n\n// Función para debounce\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,aAAa,GAAW,EAAE,WAAmB,CAAC;IAC5D,IAAI,MAAM,MAAM,OAAO;IAEvB,wDAAwD;IACxD,IAAI,KAAK,GAAG,CAAC,OAAO,UAAU,QAAQ,GAAG;QACvC,OAAO,IAAI,aAAa,CAAC;IAC3B;IAEA,uDAAuD;IACvD,IAAI,KAAK,GAAG,CAAC,OAAO,MAAM;QACxB,OAAO,IAAI,aAAa,CAAC;IAC3B;IAEA,OAAO,IAAI,cAAc,CAAC,SAAS;QACjC,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAGO,SAAS,cAAc,KAAa;IACzC,IAAI,CAAC,SAAS,MAAM,IAAI,OAAO,IAAI,OAAO;IAC1C,MAAM,MAAM,WAAW,MAAM,OAAO,CAAC,MAAM;IAC3C,OAAO,CAAC,MAAM,QAAQ,SAAS;AACjC;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI,CAAC,SAAS,MAAM,IAAI,OAAO,IAAI,OAAO;IAC1C,OAAO,WAAW,MAAM,OAAO,CAAC,MAAM;AACxC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/components/SearchBox.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Search, X, TrendingUp, Calculator } from 'lucide-react';\nimport { useStore } from '@/store/useStore';\nimport { searchService } from '@/services/searchService';\nimport { SearchResult } from '@/types';\nimport { debounce } from '@/lib/utils';\n\ninterface SearchBoxProps {\n  onClose?: () => void;\n  autoFocus?: boolean;\n}\n\nexport default function SearchBox({ onClose, autoFocus = false }: SearchBoxProps) {\n  const [results, setResults] = useState<SearchResult[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const resultsRef = useRef<HTMLDivElement>(null);\n  \n  const { searchQuery, setSearchQuery } = useStore();\n\n  // Función debounced para búsqueda\n  const debouncedSearch = debounce((query: string) => {\n    if (query.trim().length >= 2) {\n      const searchResults = searchService.search(query, 8);\n      setResults(searchResults);\n      setIsOpen(true);\n    } else {\n      setResults([]);\n      setIsOpen(false);\n    }\n    setSelectedIndex(-1);\n  }, 300);\n\n  // Efecto para realizar búsqueda\n  useEffect(() => {\n    debouncedSearch(searchQuery);\n  }, [searchQuery, debouncedSearch]);\n\n  // Efecto para auto-focus\n  useEffect(() => {\n    if (autoFocus && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [autoFocus]);\n\n  // Manejar cambio en input\n  const handleInputChange = (value: string) => {\n    setSearchQuery(value);\n  };\n\n  // Manejar teclas\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!isOpen || results.length === 0) return;\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault();\n        setSelectedIndex(prev => \n          prev < results.length - 1 ? prev + 1 : 0\n        );\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        setSelectedIndex(prev => \n          prev > 0 ? prev - 1 : results.length - 1\n        );\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (selectedIndex >= 0 && results[selectedIndex]) {\n          handleResultClick(results[selectedIndex]);\n        }\n        break;\n      case 'Escape':\n        setIsOpen(false);\n        setSelectedIndex(-1);\n        if (onClose) onClose();\n        break;\n    }\n  };\n\n  // Manejar clic en resultado\n  const handleResultClick = (result: SearchResult) => {\n    window.location.href = result.path;\n    setIsOpen(false);\n    setSearchQuery('');\n    if (onClose) onClose();\n  };\n\n  // Limpiar búsqueda\n  const clearSearch = () => {\n    setSearchQuery('');\n    setResults([]);\n    setIsOpen(false);\n    setSelectedIndex(-1);\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  // Mostrar búsquedas populares cuando no hay query\n  const showPopularSearches = searchQuery.trim().length === 0;\n  const popularSearches = searchService.getPopularSearches();\n\n  return (\n    <div className=\"relative w-full max-w-md\">\n      {/* Input de búsqueda */}\n      <div className=\"relative\">\n        <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n        <input\n          ref={inputRef}\n          type=\"text\"\n          placeholder=\"Buscar convertidor...\"\n          value={searchQuery}\n          onChange={(e) => handleInputChange(e.target.value)}\n          onKeyDown={handleKeyDown}\n          onFocus={() => {\n            if (searchQuery.trim().length >= 2 || showPopularSearches) {\n              setIsOpen(true);\n            }\n          }}\n          className=\"w-full rounded-lg border border-gray-200 bg-white pl-10 pr-10 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20\"\n        />\n        {searchQuery && (\n          <button\n            onClick={clearSearch}\n            className=\"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-4 w-4\" />\n          </button>\n        )}\n      </div>\n\n      {/* Resultados de búsqueda */}\n      {isOpen && (\n        <div\n          ref={resultsRef}\n          className=\"absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto\"\n        >\n          {showPopularSearches ? (\n            // Búsquedas populares\n            <div className=\"p-3\">\n              <div className=\"flex items-center space-x-2 mb-3\">\n                <TrendingUp className=\"h-4 w-4 text-gray-500\" />\n                <span className=\"text-sm font-medium text-gray-700\">Búsquedas populares</span>\n              </div>\n              <div className=\"space-y-1\">\n                {popularSearches.map((result, index) => (\n                  <button\n                    key={result.id}\n                    onClick={() => handleResultClick(result)}\n                    className=\"w-full text-left px-3 py-2 rounded-md hover:bg-gray-50 transition-colors\"\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <Calculator className=\"h-4 w-4 text-blue-500\" />\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900\">{result.title}</div>\n                        <div className=\"text-xs text-gray-500\">{result.description}</div>\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </div>\n            </div>\n          ) : results.length > 0 ? (\n            // Resultados de búsqueda\n            <div className=\"p-2\">\n              {results.map((result, index) => (\n                <button\n                  key={result.id}\n                  onClick={() => handleResultClick(result)}\n                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${\n                    index === selectedIndex \n                      ? 'bg-blue-50 border-blue-200' \n                      : 'hover:bg-gray-50'\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3\">\n                    <Calculator className=\"h-4 w-4 text-blue-500 mt-0.5\" />\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"text-sm font-medium text-gray-900 truncate\">\n                        {result.title}\n                      </div>\n                      <div className=\"text-xs text-gray-500 truncate\">\n                        {result.description}\n                      </div>\n                      <div className=\"text-xs text-blue-600 mt-1\">\n                        {result.category}\n                      </div>\n                    </div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          ) : searchQuery.trim().length >= 2 ? (\n            // Sin resultados\n            <div className=\"p-4 text-center text-gray-500\">\n              <Search className=\"h-8 w-8 mx-auto mb-2 text-gray-300\" />\n              <p className=\"text-sm\">No se encontraron resultados para \"{searchQuery}\"</p>\n              <p className=\"text-xs mt-1\">Intenta con términos como \"monedas\", \"temperatura\" o \"peso\"</p>\n            </div>\n          ) : null}\n        </div>\n      )}\n\n      {/* Overlay para cerrar */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAPA;;;;;;;AAce,SAAS,UAAU,EAAE,OAAO,EAAE,YAAY,KAAK,EAAkB;IAC9E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE/C,kCAAkC;IAClC,MAAM,kBAAkB,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QAChC,IAAI,MAAM,IAAI,GAAG,MAAM,IAAI,GAAG;YAC5B,MAAM,gBAAgB,gIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,OAAO;YAClD,WAAW;YACX,UAAU;QACZ,OAAO;YACL,WAAW,EAAE;YACb,UAAU;QACZ;QACA,iBAAiB,CAAC;IACpB,GAAG;IAEH,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;IAClB,GAAG;QAAC;QAAa;KAAgB;IAEjC,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS,OAAO,EAAE;YACjC,SAAS,OAAO,CAAC,KAAK;QACxB;IACF,GAAG;QAAC;KAAU;IAEd,0BAA0B;IAC1B,MAAM,oBAAoB,CAAC;QACzB,eAAe;IACjB;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,UAAU,QAAQ,MAAM,KAAK,GAAG;QAErC,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OACf,OAAO,QAAQ,MAAM,GAAG,IAAI,OAAO,IAAI;gBAEzC;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OACf,OAAO,IAAI,OAAO,IAAI,QAAQ,MAAM,GAAG;gBAEzC;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,iBAAiB,KAAK,OAAO,CAAC,cAAc,EAAE;oBAChD,kBAAkB,OAAO,CAAC,cAAc;gBAC1C;gBACA;YACF,KAAK;gBACH,UAAU;gBACV,iBAAiB,CAAC;gBAClB,IAAI,SAAS;gBACb;QACJ;IACF;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACzB,OAAO,QAAQ,CAAC,IAAI,GAAG,OAAO,IAAI;QAClC,UAAU;QACV,eAAe;QACf,IAAI,SAAS;IACf;IAEA,mBAAmB;IACnB,MAAM,cAAc;QAClB,eAAe;QACf,WAAW,EAAE;QACb,UAAU;QACV,iBAAiB,CAAC;QAClB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;QACxB;IACF;IAEA,kDAAkD;IAClD,MAAM,sBAAsB,YAAY,IAAI,GAAG,MAAM,KAAK;IAC1D,MAAM,kBAAkB,gIAAA,CAAA,gBAAa,CAAC,kBAAkB;IAExD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,WAAW;wBACX,SAAS;4BACP,IAAI,YAAY,IAAI,GAAG,MAAM,IAAI,KAAK,qBAAqB;gCACzD,UAAU;4BACZ;wBACF;wBACA,WAAU;;;;;;oBAEX,6BACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAMlB,wBACC,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAET,sBACC,sBAAsB;8BACtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC;oCAAK,WAAU;8CAAoC;;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAqC,OAAO,KAAK;;;;;;kEAChE,8OAAC;wDAAI,WAAU;kEAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;mCARzD,OAAO,EAAE;;;;;;;;;;;;;;;2BAepB,QAAQ,MAAM,GAAG,IACnB,yBAAyB;8BACzB,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;4BAEC,SAAS,IAAM,kBAAkB;4BACjC,WAAW,CAAC,wDAAwD,EAClE,UAAU,gBACN,+BACA,oBACJ;sCAEF,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,OAAO,KAAK;;;;;;0DAEf,8OAAC;gDAAI,WAAU;0DACZ,OAAO,WAAW;;;;;;0DAErB,8OAAC;gDAAI,WAAU;0DACZ,OAAO,QAAQ;;;;;;;;;;;;;;;;;;2BAlBjB,OAAO,EAAE;;;;;;;;;2BAyBlB,YAAY,IAAI,GAAG,MAAM,IAAI,IAC/B,iBAAiB;8BACjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAE,WAAU;;gCAAU;gCAAoC;gCAAY;;;;;;;sCACvE,8OAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;2BAE5B;;;;;;YAKP,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC", "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/components/UserPreferences.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Settings, Save, RotateCcw } from 'lucide-react';\nimport { useStore } from '@/store/useStore';\nimport { UserPreferences as UserPreferencesType } from '@/types';\n\nexport default function UserPreferences() {\n  const { preferences, setPreferences } = useStore();\n  const [isOpen, setIsOpen] = useState(false);\n  const [tempPreferences, setTempPreferences] = useState<UserPreferencesType>(preferences);\n\n  // Función para guardar preferencias\n  const savePreferences = () => {\n    setPreferences(tempPreferences);\n    setIsOpen(false);\n  };\n\n  // Función para resetear a valores por defecto\n  const resetToDefaults = () => {\n    const defaultPreferences: UserPreferencesType = {\n      defaultSystem: 'metric',\n      decimalPlaces: 2,\n      theme: 'system',\n      language: 'es',\n      showHistory: true,\n      maxHistoryItems: 50,\n    };\n    setTempPreferences(defaultPreferences);\n  };\n\n  // Función para cancelar cambios\n  const cancelChanges = () => {\n    setTempPreferences(preferences);\n    setIsOpen(false);\n  };\n\n  if (!isOpen) {\n    return (\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\"\n        title=\"Configuración\"\n      >\n        <Settings className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">Configuración</span>\n      </button>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-100\">\n          <div className=\"flex items-center space-x-2\">\n            <Settings className=\"h-5 w-5 text-gray-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">Configuración</h2>\n          </div>\n          <button\n            onClick={cancelChanges}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            ✕\n          </button>\n        </div>\n\n        {/* Contenido */}\n        <div className=\"p-6 space-y-6\">\n          {/* Sistema de unidades por defecto */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Sistema de unidades preferido\n            </label>\n            <select\n              value={tempPreferences.defaultSystem}\n              onChange={(e) => setTempPreferences({\n                ...tempPreferences,\n                defaultSystem: e.target.value as 'metric' | 'imperial'\n              })}\n              className=\"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"metric\">Métrico (metros, kilogramos, Celsius)</option>\n              <option value=\"imperial\">Imperial (pies, libras, Fahrenheit)</option>\n            </select>\n          </div>\n\n          {/* Decimales */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Decimales en resultados\n            </label>\n            <select\n              value={tempPreferences.decimalPlaces}\n              onChange={(e) => setTempPreferences({\n                ...tempPreferences,\n                decimalPlaces: parseInt(e.target.value)\n              })}\n              className=\"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value={0}>Sin decimales</option>\n              <option value={1}>1 decimal</option>\n              <option value={2}>2 decimales</option>\n              <option value={3}>3 decimales</option>\n              <option value={4}>4 decimales</option>\n              <option value={6}>6 decimales</option>\n            </select>\n          </div>\n\n          {/* Tema */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Tema de la aplicación\n            </label>\n            <select\n              value={tempPreferences.theme}\n              onChange={(e) => setTempPreferences({\n                ...tempPreferences,\n                theme: e.target.value as 'light' | 'dark' | 'system'\n              })}\n              className=\"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"system\">Automático (según sistema)</option>\n              <option value=\"light\">Claro</option>\n              <option value=\"dark\">Oscuro</option>\n            </select>\n          </div>\n\n          {/* Idioma */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Idioma\n            </label>\n            <select\n              value={tempPreferences.language}\n              onChange={(e) => setTempPreferences({\n                ...tempPreferences,\n                language: e.target.value as 'es' | 'en'\n              })}\n              className=\"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"es\">Español</option>\n              <option value=\"en\">English</option>\n            </select>\n          </div>\n\n          {/* Historial */}\n          <div>\n            <div className=\"flex items-center justify-between mb-2\">\n              <label className=\"text-sm font-medium text-gray-700\">\n                Mostrar historial de conversiones\n              </label>\n              <input\n                type=\"checkbox\"\n                checked={tempPreferences.showHistory}\n                onChange={(e) => setTempPreferences({\n                  ...tempPreferences,\n                  showHistory: e.target.checked\n                })}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n            </div>\n            {tempPreferences.showHistory && (\n              <div className=\"mt-3\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Máximo de elementos en historial\n                </label>\n                <select\n                  value={tempPreferences.maxHistoryItems}\n                  onChange={(e) => setTempPreferences({\n                    ...tempPreferences,\n                    maxHistoryItems: parseInt(e.target.value)\n                  })}\n                  className=\"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value={10}>10 elementos</option>\n                  <option value={25}>25 elementos</option>\n                  <option value={50}>50 elementos</option>\n                  <option value={100}>100 elementos</option>\n                </select>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-100\">\n          <button\n            onClick={resetToDefaults}\n            className=\"flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <RotateCcw className=\"h-4 w-4\" />\n            <span>Restaurar</span>\n          </button>\n          \n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={cancelChanges}\n              className=\"px-4 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              Cancelar\n            </button>\n            <button\n              onClick={savePreferences}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <Save className=\"h-4 w-4\" />\n              <span>Guardar</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAE5E,oCAAoC;IACpC,MAAM,kBAAkB;QACtB,eAAe;QACf,UAAU;IACZ;IAEA,8CAA8C;IAC9C,MAAM,kBAAkB;QACtB,MAAM,qBAA0C;YAC9C,eAAe;YACf,eAAe;YACf,OAAO;YACP,UAAU;YACV,aAAa;YACb,iBAAiB;QACnB;QACA,mBAAmB;IACrB;IAEA,gCAAgC;IAChC,MAAM,gBAAgB;QACpB,mBAAmB;QACnB,UAAU;IACZ;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YACC,SAAS,IAAM,UAAU;YACzB,WAAU;YACV,OAAM;;8BAEN,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC;oBAAK,WAAU;8BAAmB;;;;;;;;;;;;IAGzC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;sCAEtD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAMH,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,gBAAgB,aAAa;oCACpC,UAAU,CAAC,IAAM,mBAAmB;4CAClC,GAAG,eAAe;4CAClB,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC/B;oCACA,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;;;;;;;sCAK7B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,gBAAgB,aAAa;oCACpC,UAAU,CAAC,IAAM,mBAAmB;4CAClC,GAAG,eAAe;4CAClB,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC;oCACA,WAAU;;sDAEV,8OAAC;4CAAO,OAAO;sDAAG;;;;;;sDAClB,8OAAC;4CAAO,OAAO;sDAAG;;;;;;sDAClB,8OAAC;4CAAO,OAAO;sDAAG;;;;;;sDAClB,8OAAC;4CAAO,OAAO;sDAAG;;;;;;sDAClB,8OAAC;4CAAO,OAAO;sDAAG;;;;;;sDAClB,8OAAC;4CAAO,OAAO;sDAAG;;;;;;;;;;;;;;;;;;sCAKtB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,gBAAgB,KAAK;oCAC5B,UAAU,CAAC,IAAM,mBAAmB;4CAClC,GAAG,eAAe;4CAClB,OAAO,EAAE,MAAM,CAAC,KAAK;wCACvB;oCACA,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,8OAAC;4CAAO,OAAM;sDAAO;;;;;;;;;;;;;;;;;;sCAKzB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,gBAAgB,QAAQ;oCAC/B,UAAU,CAAC,IAAM,mBAAmB;4CAClC,GAAG,eAAe;4CAClB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC1B;oCACA,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,8OAAC;4CAAO,OAAM;sDAAK;;;;;;;;;;;;;;;;;;sCAKvB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC;4CACC,MAAK;4CACL,SAAS,gBAAgB,WAAW;4CACpC,UAAU,CAAC,IAAM,mBAAmB;oDAClC,GAAG,eAAe;oDAClB,aAAa,EAAE,MAAM,CAAC,OAAO;gDAC/B;4CACA,WAAU;;;;;;;;;;;;gCAGb,gBAAgB,WAAW,kBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,gBAAgB,eAAe;4CACtC,UAAU,CAAC,IAAM,mBAAmB;oDAClC,GAAG,eAAe;oDAClB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1C;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAO;8DAAI;;;;;;8DACnB,8OAAC;oDAAO,OAAO;8DAAI;;;;;;8DACnB,8OAAC;oDAAO,OAAO;8DAAI;;;;;;8DACnB,8OAAC;oDAAO,OAAO;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAK;;;;;;;;;;;;sCAGR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1840, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Menu, X, Calculator } from 'lucide-react';\nimport SearchBox from '@/components/SearchBox';\nimport UserPreferences from '@/components/UserPreferences';\nimport { cn } from '@/lib/utils';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <Calculator className=\"h-8 w-8 text-blue-600\" />\n            <div className=\"flex flex-col\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Convertidor</h1>\n              <p className=\"text-xs text-gray-500 hidden sm:block\">Universal</p>\n            </div>\n          </div>\n\n          {/* Barra de búsqueda - Desktop */}\n          <div className=\"hidden md:flex flex-1 max-w-md mx-8\">\n            <SearchBox />\n          </div>\n\n          {/* Configuración - Desktop */}\n          <div className=\"hidden md:block\">\n            <UserPreferences />\n          </div>\n\n          {/* Botón de menú móvil */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden rounded-lg p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-900\"\n          >\n            {isMenuOpen ? (\n              <X className=\"h-6 w-6\" />\n            ) : (\n              <Menu className=\"h-6 w-6\" />\n            )}\n          </button>\n        </div>\n\n        {/* Menú móvil */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t bg-white py-4\">\n            <div className=\"space-y-4\">\n              {/* Barra de búsqueda móvil */}\n              <SearchBox />\n\n              {/* Configuración móvil */}\n              <div className=\"pt-2\">\n                <UserPreferences />\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAChD,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,+HAA<PERSON>,CAAA,UAAS;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qIAAA,CAAA,UAAe;;;;;;;;;;sCAIlB,8OAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,+HAAA,CAAA,UAAS;;;;;0CAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,qIAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON><PERSON><PERSON>, Heart } from 'lucide-react';\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"border-t bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Logo y descripción */}\n          <div className=\"md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <Calculator className=\"h-6 w-6 text-blue-600\" />\n              <span className=\"text-lg font-bold text-gray-900\">Convertidor Universal</span>\n            </div>\n            <p className=\"text-gray-600 text-sm mb-4\">\n              Herramientas de conversión gratuitas y fáciles de usar para estudiantes. \n              Convierte monedas, medidas, temperatura y mucho más con precisión y rapidez.\n            </p>\n            <div className=\"flex items-center text-sm text-gray-500\">\n              <span>Hecho con</span>\n              <Heart className=\"h-4 w-4 mx-1 text-red-500 fill-current\" />\n              <span>para estudiantes</span>\n            </div>\n          </div>\n\n          {/* Enlaces útiles */}\n          <div>\n            <h3 className=\"font-semibold text-gray-900 mb-4\">Convertidores</h3>\n            <ul className=\"space-y-2 text-sm text-gray-600\">\n              <li><a href=\"#monedas\" className=\"hover:text-blue-600 transition-colors\">Monedas</a></li>\n              <li><a href=\"#longitud\" className=\"hover:text-blue-600 transition-colors\">Longitud</a></li>\n              <li><a href=\"#peso\" className=\"hover:text-blue-600 transition-colors\">Peso</a></li>\n              <li><a href=\"#temperatura\" className=\"hover:text-blue-600 transition-colors\">Temperatura</a></li>\n              <li><a href=\"#volumen\" className=\"hover:text-blue-600 transition-colors\">Volumen</a></li>\n            </ul>\n          </div>\n\n          {/* Información */}\n          <div>\n            <h3 className=\"font-semibold text-gray-900 mb-4\">Información</h3>\n            <ul className=\"space-y-2 text-sm text-gray-600\">\n              <li><a href=\"#sobre-nosotros\" className=\"hover:text-blue-600 transition-colors\">Sobre nosotros</a></li>\n              <li><a href=\"#privacidad\" className=\"hover:text-blue-600 transition-colors\">Privacidad</a></li>\n              <li><a href=\"#terminos\" className=\"hover:text-blue-600 transition-colors\">Términos</a></li>\n              <li><a href=\"#contacto\" className=\"hover:text-blue-600 transition-colors\">Contacto</a></li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Línea divisoria y copyright */}\n        <div className=\"border-t mt-8 pt-8 text-center text-sm text-gray-500\">\n          <p>&copy; {currentYear} Convertidor Universal. Todos los derechos reservados.</p>\n          <p className=\"mt-1\">Diseñado para facilitar el aprendizaje de los estudiantes.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAIe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAEpD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAW,WAAU;0DAAwC;;;;;;;;;;;sDACzE,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAwC;;;;;;;;;;;sDAC1E,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAQ,WAAU;0DAAwC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAe,WAAU;0DAAwC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAW,WAAU;0DAAwC;;;;;;;;;;;;;;;;;;;;;;;sCAK7E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAkB,WAAU;0DAAwC;;;;;;;;;;;sDAChF,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAc,WAAU;0DAAwC;;;;;;;;;;;sDAC5E,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAwC;;;;;;;;;;;sDAC1E,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAY,WAAU;0DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMhF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCAAE;gCAAQ;gCAAY;;;;;;;sCACvB,8OAAC;4BAAE,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAK9B", "debugId": null}}, {"offset": {"line": 2336, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/components/ConversionGrid.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  DollarSign, \n  Ruler, \n  Square, \n  Box, \n  Weight, \n  Thermometer, \n  Clock, \n  Gauge, \n  HardDrive, \n  Zap,\n  ChevronRight\n} from 'lucide-react';\nimport { conversionCategories } from '@/data/conversions';\nimport { cn } from '@/lib/utils';\n\n// Mapeo de iconos\nconst iconMap = {\n  DollarSign,\n  Ruler,\n  Square,\n  Box,\n  Weight,\n  Thermometer,\n  Clock,\n  Gauge,\n  HardDrive,\n  Zap,\n};\n\nexport default function ConversionGrid() {\n  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName as keyof typeof iconMap];\n    return IconComponent || DollarSign;\n  };\n\n  const getPageSlug = (categoryId: string): string => {\n    const slugMap: Record<string, string> = {\n      'length': 'longitud',\n      'area': 'superficie',\n      'volume': 'volumen',\n      'weight': 'peso',\n      'temperature': 'temperatura',\n      'time': 'tiempo',\n      'speed': 'velocidad',\n      'digital': 'datos-digitales',\n      'energy': 'energia',\n      'pressure': 'presion',\n    };\n    return slugMap[categoryId] || categoryId;\n  };\n\n  return (\n    <section id=\"convertidores\" className=\"py-16 bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Título de la sección */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\">\n            Convertidores Disponibles\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Selecciona el tipo de conversión que necesitas. Todos nuestros convertidores son gratuitos y fáciles de usar.\n          </p>\n        </div>\n\n        {/* Convertidor de monedas destacado */}\n        <div className=\"mb-12\">\n          <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center\">\n                  <DollarSign className=\"w-8 h-8 text-green-600\" />\n                </div>\n                <div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Convertidor de Monedas</h3>\n                  <p className=\"text-gray-600\">\n                    Tasas de cambio en tiempo real para más de 18 monedas internacionales\n                  </p>\n                </div>\n              </div>\n              <a\n                href=\"/monedas\"\n                className=\"flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                <span>Convertir</span>\n                <ChevronRight className=\"w-4 h-4\" />\n              </a>\n            </div>\n          </div>\n        </div>\n\n        {/* Grid de convertidores */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {conversionCategories.map((category) => {\n            const IconComponent = getIcon(category.icon);\n            \n            return (\n              <div\n                key={category.id}\n                className={cn(\n                  \"group relative bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-200 cursor-pointer\",\n                  selectedCategory === category.id && \"ring-2 ring-blue-500 border-blue-500\"\n                )}\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {/* Icono */}\n                <div className=\"w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-100 transition-colors\">\n                  <IconComponent className=\"w-6 h-6 text-blue-600\" />\n                </div>\n\n                {/* Contenido */}\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                  {category.name}\n                </h3>\n                <p className=\"text-sm text-gray-600 mb-4\">\n                  {category.description}\n                </p>\n\n                {/* Unidades disponibles */}\n                <div className=\"flex flex-wrap gap-1 mb-4\">\n                  {category.units.slice(0, 3).map((unit) => (\n                    <span\n                      key={unit.id}\n                      className=\"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md\"\n                    >\n                      {unit.symbol}\n                    </span>\n                  ))}\n                  {category.units.length > 3 && (\n                    <span className=\"inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md\">\n                      +{category.units.length - 3}\n                    </span>\n                  )}\n                </div>\n\n                {/* Botón de acción */}\n                <a\n                  href={`/${getPageSlug(category.id)}`}\n                  className=\"w-full flex items-center justify-center space-x-2 bg-gray-50 text-gray-700 py-2 rounded-lg hover:bg-gray-100 transition-colors group-hover:bg-blue-50 group-hover:text-blue-700\"\n                >\n                  <span className=\"text-sm font-medium\">Convertir</span>\n                  <ChevronRight className=\"w-4 h-4\" />\n                </a>\n\n                {/* Indicador de selección */}\n                {selectedCategory === category.id && (\n                  <div className=\"absolute top-2 right-2 w-3 h-3 bg-blue-500 rounded-full\"></div>\n                )}\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Información adicional */}\n        <div className=\"mt-12 text-center\">\n          <p className=\"text-gray-600 mb-4\">\n            ¿No encuentras el convertidor que necesitas?\n          </p>\n          <button className=\"text-blue-600 hover:text-blue-700 font-medium\">\n            Solicitar nuevo convertidor →\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AAjBA;;;;;;AAmBA,kBAAkB;AAClB,MAAM,UAAU;IACd,YAAA,kNAAA,CAAA,aAAU;IACV,OAAA,oMAAA,CAAA,QAAK;IACL,QAAA,sMAAA,CAAA,SAAM;IACN,KAAA,gMAAA,CAAA,MAAG;IACH,QAAA,sMAAA,CAAA,SAAM;IACN,aAAA,gNAAA,CAAA,cAAW;IACX,OAAA,oMAAA,CAAA,QAAK;IACL,OAAA,oMAAA,CAAA,QAAK;IACL,WAAA,gNAAA,CAAA,YAAS;IACT,KAAA,gMAAA,CAAA,MAAG;AACL;AAEe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAiC;QAC/D,OAAO,iBAAiB,kNAAA,CAAA,aAAU;IACpC;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,UAAkC;YACtC,UAAU;YACV,QAAQ;YACR,UAAU;YACV,UAAU;YACV,eAAe;YACf,QAAQ;YACR,SAAS;YACT,WAAW;YACX,UAAU;YACV,YAAY;QACd;QACA,OAAO,OAAO,CAAC,WAAW,IAAI;IAChC;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAgB,WAAU;kBACpC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAKjC,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOhC,8OAAC;oBAAI,WAAU;8BACZ,0HAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,CAAC;wBACzB,MAAM,gBAAgB,QAAQ,SAAS,IAAI;wBAE3C,qBACE,8OAAC;4BAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4HACA,qBAAqB,SAAS,EAAE,IAAI;4BAEtC,SAAS,IAAM,oBAAoB,SAAS,EAAE;;8CAG9C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAc,WAAU;;;;;;;;;;;8CAI3B,8OAAC;oCAAG,WAAU;8CACX,SAAS,IAAI;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,SAAS,WAAW;;;;;;8CAIvB,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC/B,8OAAC;gDAEC,WAAU;0DAET,KAAK,MAAM;+CAHP,KAAK,EAAE;;;;;wCAMf,SAAS,KAAK,CAAC,MAAM,GAAG,mBACvB,8OAAC;4CAAK,WAAU;;gDAAsE;gDAClF,SAAS,KAAK,CAAC,MAAM,GAAG;;;;;;;;;;;;;8CAMhC,8OAAC;oCACC,MAAM,CAAC,CAAC,EAAE,YAAY,SAAS,EAAE,GAAG;oCACpC,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;gCAIzB,qBAAqB,SAAS,EAAE,kBAC/B,8OAAC;oCAAI,WAAU;;;;;;;2BAhDZ,SAAS,EAAE;;;;;oBAoDtB;;;;;;8BAIF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAO,WAAU;sCAAgD;;;;;;;;;;;;;;;;;;;;;;;AAO5E", "debugId": null}}, {"offset": {"line": 2672, "column": 0}, "map": {"version": 3, "sources": ["file:///H:/DEV/CLINE/ConvertidorWebApp/convertidor-web-app/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { Calculator, TrendingUp, Users, Zap } from 'lucide-react';\n\nexport default function HeroSection() {\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-16 sm:py-24\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          {/* Título principal */}\n          <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6\">\n            Convertidor\n            <span className=\"text-blue-600\"> Universal</span>\n          </h1>\n          \n          {/* Subtítulo */}\n          <p className=\"text-xl sm:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            Herramientas de conversión gratuitas y precisas diseñadas especialmente para estudiantes\n          </p>\n          \n          {/* Descripción */}\n          <p className=\"text-lg text-gray-500 mb-12 max-w-2xl mx-auto\">\n            Convierte monedas, medidas, temperatura, peso y mucho más con nuestra interfaz limpia e intuitiva. \n            Resultados en tiempo real y completamente gratis.\n          </p>\n\n          {/* Características destacadas */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\">\n            <div className=\"flex flex-col items-center p-6 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\">\n                <Calculator className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Múltiples Convertidores</h3>\n              <p className=\"text-sm text-gray-600 text-center\">\n                Monedas, medidas, temperatura, peso, volumen y más\n              </p>\n            </div>\n\n            <div className=\"flex flex-col items-center p-6 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4\">\n                <TrendingUp className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Tiempo Real</h3>\n              <p className=\"text-sm text-gray-600 text-center\">\n                Tasas de cambio actualizadas y conversiones instantáneas\n              </p>\n            </div>\n\n            <div className=\"flex flex-col items-center p-6 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4\">\n                <Users className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Para Estudiantes</h3>\n              <p className=\"text-sm text-gray-600 text-center\">\n                Diseñado pensando en las necesidades académicas\n              </p>\n            </div>\n\n            <div className=\"flex flex-col items-center p-6 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4\">\n                <Zap className=\"w-6 h-6 text-orange-600\" />\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Rápido y Preciso</h3>\n              <p className=\"text-sm text-gray-600 text-center\">\n                Resultados instantáneos con alta precisión\n              </p>\n            </div>\n          </div>\n\n          {/* Call to action */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button \n              onClick={() => document.getElementById('convertidores')?.scrollIntoView({ behavior: 'smooth' })}\n              className=\"px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl\"\n            >\n              Comenzar a Convertir\n            </button>\n            <button \n              onClick={() => document.getElementById('sobre-nosotros')?.scrollIntoView({ behavior: 'smooth' })}\n              className=\"px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg border-2 border-blue-600 hover:bg-blue-50 transition-colors\"\n            >\n              Saber Más\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Elementos decorativos */}\n      <div className=\"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\">\n        <div className=\"absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-pulse\"></div>\n        <div className=\"absolute top-40 right-20 w-16 h-16 bg-indigo-200 rounded-full opacity-20 animate-pulse delay-1000\"></div>\n        <div className=\"absolute bottom-20 left-20 w-24 h-24 bg-purple-200 rounded-full opacity-20 animate-pulse delay-2000\"></div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAIlC,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;sCAKxE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAM7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAKnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAKnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAKnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAOrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,SAAS,cAAc,CAAC,kBAAkB,eAAe;4CAAE,UAAU;wCAAS;oCAC7F,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,SAAS,cAAc,CAAC,mBAAmB,eAAe;4CAAE,UAAU;wCAAS;oCAC9F,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}]}