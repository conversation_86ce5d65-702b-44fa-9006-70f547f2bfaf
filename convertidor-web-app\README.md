# 🧮 Convertidor Universal

Una aplicación web moderna y gratuita diseñada específicamente para estudiantes, que ofrece herramientas de conversión universales con una interfaz limpia e intuitiva.

## ✨ Características

### 🔄 Convertidores Disponibles
- **💰 Monedas**: Más de 18 monedas internacionales con tasas en tiempo real
- **📏 Longitud**: Metros, kilómetros, pies, pulgadas, millas, etc.
- **⚖️ Peso y Masa**: Kilogramos, libras, onzas, toneladas, etc.
- **🌡️ Temperatura**: Celsius, Fahrenheit, Kelvin
- **📦 Volumen**: Litros, galones, metros cúbicos, etc.
- **📐 Superficie**: Metros cuadrados, hectáreas, acres, etc.
- **💾 Datos Digitales**: Bytes, KB, MB, GB, TB, PB
- **⚡ Energía**: Julios, calorías, kilovatios-hora
- **🌪️ Presión**: <PERSON><PERSON>, PSI, atmósferas, bares

### 🎯 Funcionalidades
- ⚡ **Conversiones en tiempo real** mientras escribes
- 🔍 **Búsqueda global** para encontrar convertidores rápidamente
- 📱 **Diseño responsive** para todos los dispositivos
- 📊 **Historial de conversiones** local
- ⚙️ **Configuración personalizable** (decimales, sistema métrico/imperial)
- 🎨 **Interfaz moderna y limpia**
- 🚀 **Optimizado para velocidad**

## 🚀 Inicio Rápido

### Prerrequisitos
- Node.js 18+
- npm o yarn
- Docker (opcional)

### Instalación Local

```bash
# Clonar el repositorio
git clone https://github.com/tu-usuario/convertidor-web-app.git
cd convertidor-web-app

# Instalar dependencias
npm install

# Configurar variables de entorno
cp .env.example .env
# Editar .env con tus configuraciones

# Ejecutar en modo desarrollo
npm run dev
```

La aplicación estará disponible en `http://localhost:3000`

### Con Docker

```bash
# Desarrollo
npm run docker:dev

# Producción
npm run docker:prod
```

## 🛠️ Tecnologías

- **Frontend**: Next.js 15, React 19, TypeScript
- **Estilos**: Tailwind CSS
- **Estado**: Zustand
- **Iconos**: Lucide React
- **APIs**: ExchangeRate-API (tasas de cambio)
- **Despliegue**: Docker, Vercel

## 📁 Estructura del Proyecto

```
convertidor-web-app/
├── src/
│   ├── app/                    # App Router de Next.js
│   │   ├── monedas/           # Página del convertidor de monedas
│   │   ├── longitud/          # Página del convertidor de longitud
│   │   ├── peso/              # Página del convertidor de peso
│   │   └── ...                # Otras páginas de convertidores
│   ├── components/            # Componentes React
│   │   ├── converters/        # Componentes de convertidores
│   │   ├── layout/            # Componentes de layout
│   │   ├── ads/               # Componentes de AdSense
│   │   └── seo/               # Componentes SEO
│   ├── data/                  # Datos estáticos
│   ├── lib/                   # Utilidades
│   ├── services/              # Servicios (APIs, conversiones)
│   ├── store/                 # Estado global (Zustand)
│   └── types/                 # Tipos TypeScript
├── public/                    # Archivos estáticos
├── scripts/                   # Scripts de utilidad
└── docker-compose.yml         # Configuración Docker
```

## 🔧 Configuración

### Variables de Entorno

```env
# Google AdSense (opcional)
NEXT_PUBLIC_ADSENSE_CLIENT_ID=ca-pub-xxxxxxxxxxxxxxxxx

# API de tasas de cambio (opcional)
EXCHANGE_RATE_API_KEY=your_api_key_here

# URL base para SEO
NEXT_PUBLIC_BASE_URL=https://tu-dominio.com
```

## 📱 Uso

### Convertir Monedas
1. Ve a la página de monedas
2. Selecciona las monedas de origen y destino
3. Ingresa la cantidad
4. El resultado se actualiza automáticamente

### Convertir Medidas
1. Selecciona el tipo de medida (longitud, peso, etc.)
2. Elige las unidades de origen y destino
3. Ingresa el valor
4. Ve todas las conversiones posibles

### Búsqueda
- Usa la barra de búsqueda para encontrar convertidores específicos
- Busca por términos como "metros a pies" o "celsius fahrenheit"

## 🚀 Despliegue

### Vercel (Recomendado)
1. Conecta tu repositorio a Vercel
2. Configura las variables de entorno
3. Despliega automáticamente

### Docker
```bash
# Construir imagen
npm run docker:build

# Ejecutar en producción
npm run docker:prod
```

---

Hecho con ❤️ para estudiantes de todo el mundo
