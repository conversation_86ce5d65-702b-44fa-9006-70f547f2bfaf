{"name": "convertidor-web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "docker:dev": "bash scripts/docker-dev.sh", "docker:prod": "bash scripts/docker-prod.sh", "docker:build": "docker build -t convertidor-web-app:latest .", "docker:clean": "docker-compose down && docker system prune -f", "analyze": "ANALYZE=true npm run build", "export": "next export"}, "dependencies": {"@headlessui/react": "^2.2.4", "axios": "^1.10.0", "clsx": "^2.1.1", "lucide-react": "^0.523.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}