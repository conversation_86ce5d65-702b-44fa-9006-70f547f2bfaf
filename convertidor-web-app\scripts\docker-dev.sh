#!/bin/bash

# Script para desarrollo con Docker
echo "🚀 Iniciando entorno de desarrollo con Docker..."

# Verificar si Docker está instalado
if ! command -v docker &> /dev/null; then
    echo "❌ Docker no está instalado. Por favor instala Docker primero."
    exit 1
fi

# Verificar si Docker Compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose no está instalado. Por favor instala Docker Compose primero."
    exit 1
fi

# Crear archivo .env si no existe
if [ ! -f .env ]; then
    echo "📝 Creando archivo .env desde .env.example..."
    cp .env.example .env
    echo "✅ Archivo .env creado. Por favor configura las variables necesarias."
fi

# Construir y ejecutar contenedores
echo "🔨 Construyendo contenedores..."
docker-compose -f docker-compose.yml up --build -d convertidor-dev

# Mostrar logs
echo "📋 Mostrando logs..."
docker-compose logs -f convertidor-dev

# Función para limpiar al salir
cleanup() {
    echo "🧹 Deteniendo contenedores..."
    docker-compose down
}

# Capturar señal de interrupción
trap cleanup EXIT

# Mantener el script corriendo
wait
