#!/bin/bash

# Script para producción con Docker
echo "🚀 Iniciando entorno de producción con Docker..."

# Verificar si Docker está instalado
if ! command -v docker &> /dev/null; then
    echo "❌ Docker no está instalado. Por favor instala Docker primero."
    exit 1
fi

# Verificar si Docker Compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose no está instalado. Por favor instala Docker Compose primero."
    exit 1
fi

# Verificar archivo .env
if [ ! -f .env ]; then
    echo "❌ Archivo .env no encontrado. Por favor crea uno basado en .env.example"
    exit 1
fi

# Construir imagen de producción
echo "🔨 Construyendo imagen de producción..."
docker build -t convertidor-web-app:latest .

# Ejecutar contenedor de producción
echo "🚀 Ejecutando contenedor de producción..."
docker-compose -f docker-compose.yml up -d convertidor-app

# Mostrar estado
echo "📊 Estado de los contenedores:"
docker-compose ps

echo "✅ Aplicación ejecutándose en http://localhost:3000"

# Mostrar logs
echo "📋 Mostrando logs (Ctrl+C para salir)..."
docker-compose logs -f convertidor-app
