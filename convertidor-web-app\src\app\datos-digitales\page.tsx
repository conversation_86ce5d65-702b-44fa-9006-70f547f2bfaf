import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import UniversalConverter from '@/components/converters/UniversalConverter';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Convertidor de Datos Digitales - Bytes, KB, MB, GB, TB',
  description: 'Convierte entre diferentes unidades de almacenamiento digital: bits, bytes, kilobytes, megabytes, gigabytes, terabytes, petabytes.',
  keywords: ['convertidor datos', 'bytes', 'kilobytes', 'megabytes', 'gigabytes', 'terabytes', 'almacenamiento'],
};

export default function DatosDigitalesPage() {
  return (
    <>
      <Header />
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="mb-6">
            <ol className="flex items-center space-x-2 text-sm text-gray-500">
              <li><a href="/" className="hover:text-blue-600">Inicio</a></li>
              <li>/</li>
              <li className="text-gray-900">Convertidor de Datos Digitales</li>
            </ol>
          </nav>

          {/* Título de la página */}
          <div className="mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Convertidor de Datos Digitales
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl">
              Convierte entre diferentes unidades de almacenamiento digital. Desde bits y bytes 
              hasta terabytes y petabytes. Ideal para estudiantes de informática y tecnología.
            </p>
          </div>

          {/* Convertidor */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <UniversalConverter categoryId="digital" />
            </div>

            {/* Panel lateral */}
            <div className="space-y-6">
              {/* Conversiones comunes */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Conversiones Comunes</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 Byte</span>
                    <span className="font-medium">8 bits</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 KB</span>
                    <span className="font-medium">1,024 Bytes</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 MB</span>
                    <span className="font-medium">1,024 KB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 GB</span>
                    <span className="font-medium">1,024 MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 TB</span>
                    <span className="font-medium">1,024 GB</span>
                  </div>
                </div>
              </div>

              {/* Referencias de tamaño */}
              <div className="bg-purple-50 rounded-xl border border-purple-100 p-6">
                <h3 className="text-lg font-semibold text-purple-900 mb-3">Referencias de Tamaño</h3>
                <div className="space-y-2 text-sm text-purple-800">
                  <p>• Carácter de texto ≈ 1 Byte</p>
                  <p>• Página de texto ≈ 2 KB</p>
                  <p>• Foto digital ≈ 2-5 MB</p>
                  <p>• Canción MP3 ≈ 3-5 MB</p>
                  <p>• Video HD (1 hora) ≈ 3-4 GB</p>
                  <p>• Juego moderno ≈ 50-100 GB</p>
                </div>
              </div>

              {/* Sistema binario */}
              <div className="bg-blue-50 rounded-xl border border-blue-100 p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">Sistema Binario</h3>
                <div className="space-y-2 text-sm text-blue-800">
                  <p>• Las computadoras usan base 2 (binario)</p>
                  <p>• 1 KB = 1,024 Bytes (no 1,000)</p>
                  <p>• Cada nivel multiplica por 1,024</p>
                  <p>• 1,024 = 2¹⁰ (potencia de 2)</p>
                </div>
              </div>

              {/* Consejos */}
              <div className="bg-green-50 rounded-xl border border-green-100 p-6">
                <h3 className="text-lg font-semibold text-green-900 mb-3">Consejos de Uso</h3>
                <div className="space-y-2 text-sm text-green-800">
                  <p>• Byte es la unidad básica de almacenamiento</p>
                  <p>• GB se usa para archivos grandes</p>
                  <p>• TB se usa para discos duros</p>
                  <p>• PB se usa en centros de datos</p>
                </div>
              </div>
            </div>
          </div>

          {/* Información adicional */}
          <div className="mt-12 bg-white rounded-xl shadow-sm border border-gray-100 p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Jerarquía de Unidades Digitales</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Unidades Básicas</h3>
                <div className="space-y-2 text-gray-600">
                  <div className="flex justify-between">
                    <span>Bit</span>
                    <span>Unidad más pequeña (0 o 1)</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Byte (B)</span>
                    <span>8 bits</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Kilobyte (KB)</span>
                    <span>1,024 Bytes</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Megabyte (MB)</span>
                    <span>1,024 KB</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Unidades Grandes</h3>
                <div className="space-y-2 text-gray-600">
                  <div className="flex justify-between">
                    <span>Gigabyte (GB)</span>
                    <span>1,024 MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Terabyte (TB)</span>
                    <span>1,024 GB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Petabyte (PB)</span>
                    <span>1,024 TB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Exabyte (EB)</span>
                    <span>1,024 PB</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
