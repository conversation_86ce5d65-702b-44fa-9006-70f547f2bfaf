import type { Metadata } from "next";
import { Inter } from "next/font/google";
import AdSenseScript from "@/components/ads/AdSenseScript";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Convertidor Universal - Herramientas de Conversión para Estudiantes",
  description: "Convertidor universal gratuito para estudiantes. Convierte monedas, medidas, temperatura, peso, volumen y más. Interfaz limpia y fácil de usar.",
  keywords: ["convertidor", "conversión", "monedas", "medidas", "temperatura", "estudiantes", "herramientas", "calculadora"],
  authors: [{ name: "Convertidor Universal" }],
  creator: "Convertidor Universal",
  publisher: "Convertidor Universal",
  robots: "index, follow",
  openGraph: {
    title: "Convertidor Universal - Herramientas de Conversión",
    description: "Convertidor universal gratuito para estudiantes. Convierte monedas, medidas, temperatura y más.",
    type: "website",
    locale: "es_ES",
  },
  twitter: {
    card: "summary_large_image",
    title: "Convertidor Universal - Herramientas de Conversión",
    description: "Convertidor universal gratuito para estudiantes.",
  },
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#3b82f6",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es" className={inter.variable}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#3b82f6" />
      </head>
      <body className="font-sans antialiased bg-gray-50 text-gray-900">
        <AdSenseScript />
        <div className="min-h-screen flex flex-col">
          {children}
        </div>
      </body>
    </html>
  );
}
