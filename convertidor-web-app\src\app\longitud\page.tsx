import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import UniversalConverter from '@/components/converters/UniversalConverter';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Convertidor de Longitud - Metros, Pies, Pulgadas, Kilómetros',
  description: 'Convierte entre diferentes unidades de longitud: metros, kilómetros, centímetros, pulgadas, pies, yardas y millas. Herramienta gratuita para estudiantes.',
  keywords: ['convertidor longitud', 'metros a pies', 'pulgadas a centímetros', 'kilómetros a millas', 'medidas longitud'],
};

export default function LongitudPage() {
  return (
    <>
      <Header />
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="mb-6">
            <ol className="flex items-center space-x-2 text-sm text-gray-500">
              <li><a href="/" className="hover:text-blue-600">Inicio</a></li>
              <li>/</li>
              <li className="text-gray-900">Convertidor de Longitud</li>
            </ol>
          </nav>

          {/* Título de la página */}
          <div className="mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Convertidor de Longitud
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl">
              Convierte fácilmente entre diferentes unidades de longitud. Incluye unidades métricas 
              (metros, kilómetros, centímetros) e imperiales (pies, pulgadas, yardas, millas).
            </p>
          </div>

          {/* Convertidor */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <UniversalConverter categoryId="length" />
            </div>

            {/* Panel lateral */}
            <div className="space-y-6">
              {/* Conversiones comunes */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Conversiones Comunes</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 metro</span>
                    <span className="font-medium">3.28 pies</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 kilómetro</span>
                    <span className="font-medium">0.62 millas</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 pulgada</span>
                    <span className="font-medium">2.54 cm</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 pie</span>
                    <span className="font-medium">30.48 cm</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 yarda</span>
                    <span className="font-medium">0.91 metros</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 milla</span>
                    <span className="font-medium">1.61 km</span>
                  </div>
                </div>
              </div>

              {/* Información útil */}
              <div className="bg-blue-50 rounded-xl border border-blue-100 p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">Sistemas de Medida</h3>
                <div className="space-y-3 text-sm text-blue-800">
                  <div>
                    <strong>Sistema Métrico:</strong>
                    <p>Usado mundialmente. Base 10, fácil de convertir.</p>
                  </div>
                  <div>
                    <strong>Sistema Imperial:</strong>
                    <p>Usado principalmente en EE.UU. y Reino Unido.</p>
                  </div>
                </div>
              </div>

              {/* Consejos */}
              <div className="bg-green-50 rounded-xl border border-green-100 p-6">
                <h3 className="text-lg font-semibold text-green-900 mb-3">Consejos de Uso</h3>
                <div className="space-y-2 text-sm text-green-800">
                  <p>• Para medidas precisas, usa más decimales</p>
                  <p>• El metro es la unidad base del sistema métrico</p>
                  <p>• 1 pie = 12 pulgadas</p>
                  <p>• 1 yarda = 3 pies</p>
                  <p>• 1 milla = 5,280 pies</p>
                </div>
              </div>
            </div>
          </div>

          {/* Información adicional */}
          <div className="mt-12 bg-white rounded-xl shadow-sm border border-gray-100 p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Unidades de Longitud Disponibles</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Sistema Métrico</h3>
                <div className="space-y-2 text-gray-600">
                  <div className="flex justify-between">
                    <span>Kilómetro (km)</span>
                    <span>1,000 metros</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Metro (m)</span>
                    <span>Unidad base</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Centímetro (cm)</span>
                    <span>0.01 metros</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Milímetro (mm)</span>
                    <span>0.001 metros</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Sistema Imperial</h3>
                <div className="space-y-2 text-gray-600">
                  <div className="flex justify-between">
                    <span>Milla (mi)</span>
                    <span>5,280 pies</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Yarda (yd)</span>
                    <span>3 pies</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Pie (ft)</span>
                    <span>12 pulgadas</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Pulgada (in)</span>
                    <span>Unidad base imperial</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
