import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import CurrencyConverter from '@/components/converters/CurrencyConverter';
import ConversionHistory from '@/components/ConversionHistory';
import { SidebarAd, MobileAd } from '@/components/ads/AdSenseAd';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Convertidor de Monedas - Tasas de Cambio en Tiempo Real',
  description: 'Convierte entre más de 18 monedas internacionales con tasas de cambio actualizadas en tiempo real. Incluye USD, EUR, GBP, JPY y monedas latinoamericanas.',
  keywords: ['convertidor monedas', 'tasas cambio', 'divisas', 'USD', 'EUR', 'peso mexicano', 'real brasileño'],
};

export default function MonedasPage() {
  return (
    <>
      <Header />
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="mb-6">
            <ol className="flex items-center space-x-2 text-sm text-gray-500">
              <li><a href="/" className="hover:text-blue-600">Inicio</a></li>
              <li>/</li>
              <li className="text-gray-900">Convertidor de Monedas</li>
            </ol>
          </nav>

          {/* Título de la página */}
          <div className="mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Convertidor de Monedas
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl">
              Convierte entre las principales monedas del mundo con tasas de cambio actualizadas en tiempo real. 
              Incluye monedas de América Latina, Estados Unidos, Europa y Asia.
            </p>
          </div>

          {/* Convertidor principal */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              <CurrencyConverter />
              <MobileAd />
              <ConversionHistory />
            </div>

            {/* Panel lateral con información */}
            <div className="space-y-6">
              {/* Anuncio en sidebar */}
              <SidebarAd />

              {/* Monedas populares */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Monedas Populares</h3>
                <div className="space-y-3">
                  {[
                    { code: 'USD', name: 'Dólar estadounidense', flag: '🇺🇸' },
                    { code: 'EUR', name: 'Euro', flag: '🇪🇺' },
                    { code: 'GBP', name: 'Libra esterlina', flag: '🇬🇧' },
                    { code: 'JPY', name: 'Yen japonés', flag: '🇯🇵' },
                    { code: 'MXN', name: 'Peso mexicano', flag: '🇲🇽' },
                    { code: 'BRL', name: 'Real brasileño', flag: '🇧🇷' },
                  ].map((currency) => (
                    <div key={currency.code} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg">
                      <span className="text-xl">{currency.flag}</span>
                      <div>
                        <div className="font-medium text-gray-900">{currency.code}</div>
                        <div className="text-sm text-gray-500">{currency.name}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Información sobre tasas */}
              <div className="bg-blue-50 rounded-xl border border-blue-100 p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">Sobre las Tasas de Cambio</h3>
                <div className="space-y-2 text-sm text-blue-800">
                  <p>• Las tasas se actualizan cada 10 minutos</p>
                  <p>• Datos obtenidos de fuentes financieras confiables</p>
                  <p>• Precisión de hasta 4 decimales</p>
                  <p>• Incluye más de 18 monedas internacionales</p>
                </div>
              </div>

              {/* Consejos */}
              <div className="bg-green-50 rounded-xl border border-green-100 p-6">
                <h3 className="text-lg font-semibold text-green-900 mb-3">Consejos de Uso</h3>
                <div className="space-y-2 text-sm text-green-800">
                  <p>• Usa el botón de intercambio para cambiar rápidamente las monedas</p>
                  <p>• Los resultados se actualizan automáticamente mientras escribes</p>
                  <p>• Haz clic en "Actualizar" para obtener las tasas más recientes</p>
                  <p>• Ideal para estudiantes de economía y viajeros</p>
                </div>
              </div>
            </div>
          </div>

          {/* Información adicional */}
          <div className="mt-12 bg-white rounded-xl shadow-sm border border-gray-100 p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Monedas Disponibles</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { region: 'América del Norte', currencies: ['USD - Dólar estadounidense', 'CAD - Dólar canadiense', 'MXN - Peso mexicano'] },
                { region: 'Europa', currencies: ['EUR - Euro', 'GBP - Libra esterlina'] },
                { region: 'Asia-Pacífico', currencies: ['JPY - Yen japonés', 'AUD - Dólar australiano'] },
                { region: 'América Central', currencies: ['CRC - Colón costarricense', 'GTQ - Quetzal guatemalteco', 'HNL - Lempira hondureño'] },
                { region: 'América del Sur', currencies: ['BRL - Real brasileño', 'ARS - Peso argentino', 'CLP - Peso chileno', 'COP - Peso colombiano', 'PEN - Sol peruano'] },
                { region: 'Caribe', currencies: ['DOP - Peso dominicano', 'PAB - Balboa panameño', 'NIO - Córdoba nicaragüense'] },
              ].map((region) => (
                <div key={region.region} className="space-y-2">
                  <h3 className="font-semibold text-gray-900">{region.region}</h3>
                  <ul className="space-y-1 text-sm text-gray-600">
                    {region.currencies.map((currency) => (
                      <li key={currency}>• {currency}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
