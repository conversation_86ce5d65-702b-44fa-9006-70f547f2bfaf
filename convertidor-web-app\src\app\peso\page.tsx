import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import UniversalConverter from '@/components/converters/UniversalConverter';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Convertidor de Peso y Masa - Kilogramos, Libras, Onzas',
  description: 'Convierte entre diferentes unidades de peso y masa: kilogramos, gramos, libras, onzas, toneladas. Herramienta gratuita para estudiantes.',
  keywords: ['convertidor peso', 'kilogramos a libras', 'gramos a onzas', 'toneladas', 'masa'],
};

export default function PesoPage() {
  return (
    <>
      <Header />
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="mb-6">
            <ol className="flex items-center space-x-2 text-sm text-gray-500">
              <li><a href="/" className="hover:text-blue-600">Inicio</a></li>
              <li>/</li>
              <li className="text-gray-900">Convertidor de Peso</li>
            </ol>
          </nav>

          {/* Título de la página */}
          <div className="mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Convertidor de Peso y Masa
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl">
              Convierte entre diferentes unidades de peso y masa. Incluye kilogramos, gramos, 
              libras, onzas, toneladas métricas y piedras.
            </p>
          </div>

          {/* Convertidor */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <UniversalConverter categoryId="weight" />
            </div>

            {/* Panel lateral */}
            <div className="space-y-6">
              {/* Conversiones comunes */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Conversiones Comunes</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 kilogramo</span>
                    <span className="font-medium">2.20 libras</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 libra</span>
                    <span className="font-medium">453.59 gramos</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 onza</span>
                    <span className="font-medium">28.35 gramos</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 tonelada</span>
                    <span className="font-medium">1,000 kg</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">1 piedra</span>
                    <span className="font-medium">6.35 kg</span>
                  </div>
                </div>
              </div>

              {/* Información útil */}
              <div className="bg-purple-50 rounded-xl border border-purple-100 p-6">
                <h3 className="text-lg font-semibold text-purple-900 mb-3">Diferencia: Peso vs Masa</h3>
                <div className="space-y-3 text-sm text-purple-800">
                  <div>
                    <strong>Masa:</strong>
                    <p>Cantidad de materia en un objeto. No cambia con la ubicación.</p>
                  </div>
                  <div>
                    <strong>Peso:</strong>
                    <p>Fuerza gravitacional sobre un objeto. Varía según la gravedad.</p>
                  </div>
                  <p className="text-xs">En uso cotidiano, ambos términos se usan indistintamente.</p>
                </div>
              </div>

              {/* Consejos */}
              <div className="bg-green-50 rounded-xl border border-green-100 p-6">
                <h3 className="text-lg font-semibold text-green-900 mb-3">Consejos de Uso</h3>
                <div className="space-y-2 text-sm text-green-800">
                  <p>• El kilogramo es la unidad base del SI</p>
                  <p>• 1 libra = 16 onzas</p>
                  <p>• 1 piedra = 14 libras (usado en Reino Unido)</p>
                  <p>• Para cocinar, usa gramos para mayor precisión</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
