'use client';

import { useState } from 'react';
import { 
  DollarSign, 
  Ruler, 
  Square, 
  Box, 
  Weight, 
  Thermometer, 
  Clock, 
  Gauge, 
  HardDrive, 
  Zap,
  ChevronRight
} from 'lucide-react';
import { conversionCategories } from '@/data/conversions';
import { cn } from '@/lib/utils';

// Mapeo de iconos
const iconMap = {
  DollarSign,
  Ruler,
  Square,
  Box,
  Weight,
  Thermometer,
  Clock,
  Gauge,
  HardDrive,
  Zap,
};

export default function ConversionGrid() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap];
    return IconComponent || DollarSign;
  };

  const getPageSlug = (categoryId: string): string => {
    const slugMap: Record<string, string> = {
      'length': 'longitud',
      'area': 'superficie',
      'volume': 'volumen',
      'weight': 'peso',
      'temperature': 'temperatura',
      'time': 'tiempo',
      'speed': 'velocidad',
      'digital': 'datos-digitales',
      'energy': 'energia',
      'pressure': 'presion',
    };
    return slugMap[categoryId] || categoryId;
  };

  return (
    <section id="convertidores" className="py-16 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Título de la sección */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Convertidores Disponibles
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Selecciona el tipo de conversión que necesitas. Todos nuestros convertidores son gratuitos y fáciles de usar.
          </p>
        </div>

        {/* Convertidor de monedas destacado */}
        <div className="mb-12">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center">
                  <DollarSign className="w-8 h-8 text-green-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Convertidor de Monedas</h3>
                  <p className="text-gray-600">
                    Tasas de cambio en tiempo real para más de 18 monedas internacionales
                  </p>
                </div>
              </div>
              <a
                href="/monedas"
                className="flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
              >
                <span>Convertir</span>
                <ChevronRight className="w-4 h-4" />
              </a>
            </div>
          </div>
        </div>

        {/* Grid de convertidores */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {conversionCategories.map((category) => {
            const IconComponent = getIcon(category.icon);
            
            return (
              <div
                key={category.id}
                className={cn(
                  "group relative bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-200 cursor-pointer",
                  selectedCategory === category.id && "ring-2 ring-blue-500 border-blue-500"
                )}
                onClick={() => setSelectedCategory(category.id)}
              >
                {/* Icono */}
                <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-100 transition-colors">
                  <IconComponent className="w-6 h-6 text-blue-600" />
                </div>

                {/* Contenido */}
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {category.name}
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {category.description}
                </p>

                {/* Unidades disponibles */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {category.units.slice(0, 3).map((unit) => (
                    <span
                      key={unit.id}
                      className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md"
                    >
                      {unit.symbol}
                    </span>
                  ))}
                  {category.units.length > 3 && (
                    <span className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md">
                      +{category.units.length - 3}
                    </span>
                  )}
                </div>

                {/* Botón de acción */}
                <a
                  href={`/${getPageSlug(category.id)}`}
                  className="w-full flex items-center justify-center space-x-2 bg-gray-50 text-gray-700 py-2 rounded-lg hover:bg-gray-100 transition-colors group-hover:bg-blue-50 group-hover:text-blue-700"
                >
                  <span className="text-sm font-medium">Convertir</span>
                  <ChevronRight className="w-4 h-4" />
                </a>

                {/* Indicador de selección */}
                {selectedCategory === category.id && (
                  <div className="absolute top-2 right-2 w-3 h-3 bg-blue-500 rounded-full"></div>
                )}
              </div>
            );
          })}
        </div>

        {/* Información adicional */}
        <div className="mt-12 text-center">
          <p className="text-gray-600 mb-4">
            ¿No encuentras el convertidor que necesitas?
          </p>
          <button className="text-blue-600 hover:text-blue-700 font-medium">
            Solicitar nuevo convertidor →
          </button>
        </div>
      </div>
    </section>
  );
}
