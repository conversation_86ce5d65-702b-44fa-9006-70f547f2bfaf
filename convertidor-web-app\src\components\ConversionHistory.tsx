'use client';

import { useState } from 'react';
import { History, Trash2, Co<PERSON>, Check, Clock, X } from 'lucide-react';
import { useStore } from '@/store/useStore';
import { formatNumber } from '@/lib/utils';
import { ConversionResult } from '@/types';

export default function ConversionHistory() {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  
  const { conversionHistory, clearHistory, preferences } = useStore();

  if (!preferences.showHistory || conversionHistory.length === 0) {
    return null;
  }

  // Función para copiar al portapapeles
  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Error copying to clipboard:', err);
    }
  };

  // Función para formatear la fecha
  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Ahora mismo';
    if (diffMins < 60) return `Hace ${diffMins} min`;
    if (diffHours < 24) return `Hace ${diffHours}h`;
    if (diffDays < 7) return `Hace ${diffDays}d`;
    
    return date.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short'
    });
  };

  // Función para obtener el nombre de la categoría
  const getCategoryName = (category: string) => {
    const categoryNames: Record<string, string> = {
      'currency': 'Monedas',
      'length': 'Longitud',
      'weight': 'Peso',
      'temperature': 'Temperatura',
      'volume': 'Volumen',
      'area': 'Superficie',
      'time': 'Tiempo',
      'speed': 'Velocidad',
      'digital': 'Datos',
      'energy': 'Energía',
      'pressure': 'Presión',
    };
    return categoryNames[category] || category;
  };

  const displayedHistory = isExpanded ? conversionHistory : conversionHistory.slice(0, 5);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <History className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Historial Reciente</h3>
          <span className="text-sm text-gray-500">({conversionHistory.length})</span>
        </div>
        <div className="flex items-center space-x-2">
          {conversionHistory.length > 5 && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              {isExpanded ? 'Ver menos' : `Ver todos (${conversionHistory.length})`}
            </button>
          )}
          <button
            onClick={clearHistory}
            className="p-1 text-gray-400 hover:text-red-500 transition-colors"
            title="Limpiar historial"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Lista de conversiones */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {displayedHistory.map((conversion, index) => (
          <div
            key={`${conversion.timestamp.getTime()}-${index}`}
            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div className="flex-1 min-w-0">
              {/* Conversión principal */}
              <div className="flex items-center space-x-2 mb-1">
                <span className="font-medium text-gray-900">
                  {formatNumber(conversion.fromValue, preferences.decimalPlaces)} {conversion.fromUnit.symbol}
                </span>
                <span className="text-gray-400">→</span>
                <span className="font-medium text-blue-600">
                  {formatNumber(conversion.toValue, preferences.decimalPlaces)} {conversion.toUnit.symbol}
                </span>
              </div>
              
              {/* Información adicional */}
              <div className="flex items-center space-x-3 text-xs text-gray-500">
                <span className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span>{formatDate(conversion.timestamp)}</span>
                </span>
                <span className="px-2 py-0.5 bg-gray-200 rounded-full">
                  {getCategoryName(conversion.category)}
                </span>
              </div>
            </div>

            {/* Acciones */}
            <div className="flex items-center space-x-1 ml-3">
              <button
                onClick={() => copyToClipboard(
                  `${formatNumber(conversion.toValue, preferences.decimalPlaces)} ${conversion.toUnit.symbol}`,
                  index
                )}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="Copiar resultado"
              >
                {copiedIndex === index ? (
                  <Check className="h-4 w-4 text-green-500" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Mensaje cuando está vacío */}
      {conversionHistory.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <History className="h-12 w-12 mx-auto mb-3 text-gray-300" />
          <p className="text-sm">No hay conversiones recientes</p>
          <p className="text-xs mt-1">Tus conversiones aparecerán aquí automáticamente</p>
        </div>
      )}

      {/* Información sobre el historial */}
      {conversionHistory.length > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-100">
          <p className="text-xs text-gray-500 text-center">
            Se guardan las últimas {preferences.maxHistoryItems} conversiones localmente
          </p>
        </div>
      )}
    </div>
  );
}
