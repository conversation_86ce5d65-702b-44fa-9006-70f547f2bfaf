'use client';

import { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export default class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error capturado por ErrorBoundary:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
            {/* Icono de error */}
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>

            {/* Título */}
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              ¡Oops! Algo salió mal
            </h1>

            {/* Descripción */}
            <p className="text-gray-600 mb-6">
              Ha ocurrido un error inesperado. No te preocupes, puedes intentar recargar la página 
              o volver al inicio.
            </p>

            {/* Detalles del error (solo en desarrollo) */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="mb-6 p-4 bg-gray-100 rounded-lg text-left">
                <h3 className="font-semibold text-gray-900 mb-2">Detalles del error:</h3>
                <pre className="text-xs text-gray-700 overflow-auto">
                  {this.state.error.message}
                </pre>
              </div>
            )}

            {/* Acciones */}
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={this.handleReload}
                className="flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Recargar página</span>
              </button>
              
              <button
                onClick={this.handleGoHome}
                className="flex items-center justify-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <Home className="w-4 h-4" />
                <span>Ir al inicio</span>
              </button>
            </div>

            {/* Información adicional */}
            <div className="mt-6 pt-6 border-t border-gray-100">
              <p className="text-sm text-gray-500">
                Si el problema persiste, intenta:
              </p>
              <ul className="text-sm text-gray-500 mt-2 space-y-1">
                <li>• Limpiar la caché del navegador</li>
                <li>• Verificar tu conexión a internet</li>
                <li>• Usar un navegador diferente</li>
              </ul>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Componente para errores específicos de conversión
export function ConversionError({ 
  message, 
  onRetry, 
  onReset 
}: { 
  message: string; 
  onRetry?: () => void; 
  onReset?: () => void; 
}) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
      <div className="flex items-start space-x-3">
        <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800">Error en la conversión</h3>
          <p className="text-sm text-red-700 mt-1">{message}</p>
          
          {(onRetry || onReset) && (
            <div className="flex items-center space-x-3 mt-3">
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="text-sm text-red-800 hover:text-red-900 font-medium"
                >
                  Reintentar
                </button>
              )}
              {onReset && (
                <button
                  onClick={onReset}
                  className="text-sm text-red-600 hover:text-red-700"
                >
                  Restablecer
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Hook para manejo de errores en componentes funcionales
export function useErrorHandler() {
  const handleError = (error: Error, context?: string) => {
    console.error(`Error${context ? ` en ${context}` : ''}:`, error);
    
    // Aquí podrías enviar el error a un servicio de monitoreo
    // como Sentry, LogRocket, etc.
    
    return {
      message: error.message || 'Ha ocurrido un error inesperado',
      timestamp: new Date(),
      context
    };
  };

  return { handleError };
}
