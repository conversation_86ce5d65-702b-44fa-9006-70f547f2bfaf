'use client';

import { useState, useEffect, useRef } from 'react';
import { Search, X, TrendingUp, Calculator } from 'lucide-react';
import { useStore } from '@/store/useStore';
import { searchService } from '@/services/searchService';
import { SearchResult } from '@/types';
import { debounce } from '@/lib/utils';

interface SearchBoxProps {
  onClose?: () => void;
  autoFocus?: boolean;
}

export default function SearchBox({ onClose, autoFocus = false }: SearchBoxProps) {
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  
  const { searchQuery, setSearchQuery } = useStore();

  // Función debounced para búsqueda
  const debouncedSearch = debounce((query: string) => {
    if (query.trim().length >= 2) {
      const searchResults = searchService.search(query, 8);
      setResults(searchResults);
      setIsOpen(true);
    } else {
      setResults([]);
      setIsOpen(false);
    }
    setSelectedIndex(-1);
  }, 300);

  // Efecto para realizar búsqueda
  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);

  // Efecto para auto-focus
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Manejar cambio en input
  const handleInputChange = (value: string) => {
    setSearchQuery(value);
  };

  // Manejar teclas
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : results.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && results[selectedIndex]) {
          handleResultClick(results[selectedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        if (onClose) onClose();
        break;
    }
  };

  // Manejar clic en resultado
  const handleResultClick = (result: SearchResult) => {
    window.location.href = result.path;
    setIsOpen(false);
    setSearchQuery('');
    if (onClose) onClose();
  };

  // Limpiar búsqueda
  const clearSearch = () => {
    setSearchQuery('');
    setResults([]);
    setIsOpen(false);
    setSelectedIndex(-1);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Mostrar búsquedas populares cuando no hay query
  const showPopularSearches = searchQuery.trim().length === 0;
  const popularSearches = searchService.getPopularSearches();

  return (
    <div className="relative w-full max-w-md">
      {/* Input de búsqueda */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          placeholder="Buscar convertidor..."
          value={searchQuery}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (searchQuery.trim().length >= 2 || showPopularSearches) {
              setIsOpen(true);
            }
          }}
          className="w-full rounded-lg border border-gray-200 bg-white pl-10 pr-10 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
        />
        {searchQuery && (
          <button
            onClick={clearSearch}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Resultados de búsqueda */}
      {isOpen && (
        <div
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
        >
          {showPopularSearches ? (
            // Búsquedas populares
            <div className="p-3">
              <div className="flex items-center space-x-2 mb-3">
                <TrendingUp className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Búsquedas populares</span>
              </div>
              <div className="space-y-1">
                {popularSearches.map((result, index) => (
                  <button
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <Calculator className="h-4 w-4 text-blue-500" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{result.title}</div>
                        <div className="text-xs text-gray-500">{result.description}</div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ) : results.length > 0 ? (
            // Resultados de búsqueda
            <div className="p-2">
              {results.map((result, index) => (
                <button
                  key={result.id}
                  onClick={() => handleResultClick(result)}
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    index === selectedIndex 
                      ? 'bg-blue-50 border-blue-200' 
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <Calculator className="h-4 w-4 text-blue-500 mt-0.5" />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {result.title}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {result.description}
                      </div>
                      <div className="text-xs text-blue-600 mt-1">
                        {result.category}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : searchQuery.trim().length >= 2 ? (
            // Sin resultados
            <div className="p-4 text-center text-gray-500">
              <Search className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No se encontraron resultados para "{searchQuery}"</p>
              <p className="text-xs mt-1">Intenta con términos como "monedas", "temperatura" o "peso"</p>
            </div>
          ) : null}
        </div>
      )}

      {/* Overlay para cerrar */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
