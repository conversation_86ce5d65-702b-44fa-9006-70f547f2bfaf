'use client';

import { useState } from 'react';
import { Settings, Save, RotateCcw } from 'lucide-react';
import { useStore } from '@/store/useStore';
import { UserPreferences as UserPreferencesType } from '@/types';

export default function UserPreferences() {
  const { preferences, setPreferences } = useStore();
  const [isOpen, setIsOpen] = useState(false);
  const [tempPreferences, setTempPreferences] = useState<UserPreferencesType>(preferences);

  // Función para guardar preferencias
  const savePreferences = () => {
    setPreferences(tempPreferences);
    setIsOpen(false);
  };

  // Función para resetear a valores por defecto
  const resetToDefaults = () => {
    const defaultPreferences: UserPreferencesType = {
      defaultSystem: 'metric',
      decimalPlaces: 2,
      theme: 'system',
      language: 'es',
      showHistory: true,
      maxHistoryItems: 50,
    };
    setTempPreferences(defaultPreferences);
  };

  // Función para cancelar cambios
  const cancelChanges = () => {
    setTempPreferences(preferences);
    setIsOpen(false);
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        title="Configuración"
      >
        <Settings className="h-4 w-4" />
        <span className="hidden sm:inline">Configuración</span>
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-gray-600" />
            <h2 className="text-lg font-semibold text-gray-900">Configuración</h2>
          </div>
          <button
            onClick={cancelChanges}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {/* Contenido */}
        <div className="p-6 space-y-6">
          {/* Sistema de unidades por defecto */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sistema de unidades preferido
            </label>
            <select
              value={tempPreferences.defaultSystem}
              onChange={(e) => setTempPreferences({
                ...tempPreferences,
                defaultSystem: e.target.value as 'metric' | 'imperial'
              })}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="metric">Métrico (metros, kilogramos, Celsius)</option>
              <option value="imperial">Imperial (pies, libras, Fahrenheit)</option>
            </select>
          </div>

          {/* Decimales */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Decimales en resultados
            </label>
            <select
              value={tempPreferences.decimalPlaces}
              onChange={(e) => setTempPreferences({
                ...tempPreferences,
                decimalPlaces: parseInt(e.target.value)
              })}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={0}>Sin decimales</option>
              <option value={1}>1 decimal</option>
              <option value={2}>2 decimales</option>
              <option value={3}>3 decimales</option>
              <option value={4}>4 decimales</option>
              <option value={6}>6 decimales</option>
            </select>
          </div>

          {/* Tema */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tema de la aplicación
            </label>
            <select
              value={tempPreferences.theme}
              onChange={(e) => setTempPreferences({
                ...tempPreferences,
                theme: e.target.value as 'light' | 'dark' | 'system'
              })}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="system">Automático (según sistema)</option>
              <option value="light">Claro</option>
              <option value="dark">Oscuro</option>
            </select>
          </div>

          {/* Idioma */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Idioma
            </label>
            <select
              value={tempPreferences.language}
              onChange={(e) => setTempPreferences({
                ...tempPreferences,
                language: e.target.value as 'es' | 'en'
              })}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="es">Español</option>
              <option value="en">English</option>
            </select>
          </div>

          {/* Historial */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">
                Mostrar historial de conversiones
              </label>
              <input
                type="checkbox"
                checked={tempPreferences.showHistory}
                onChange={(e) => setTempPreferences({
                  ...tempPreferences,
                  showHistory: e.target.checked
                })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            {tempPreferences.showHistory && (
              <div className="mt-3">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Máximo de elementos en historial
                </label>
                <select
                  value={tempPreferences.maxHistoryItems}
                  onChange={(e) => setTempPreferences({
                    ...tempPreferences,
                    maxHistoryItems: parseInt(e.target.value)
                  })}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={10}>10 elementos</option>
                  <option value={25}>25 elementos</option>
                  <option value={50}>50 elementos</option>
                  <option value={100}>100 elementos</option>
                </select>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-100">
          <button
            onClick={resetToDefaults}
            className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <RotateCcw className="h-4 w-4" />
            <span>Restaurar</span>
          </button>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={cancelChanges}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={savePreferences}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Save className="h-4 w-4" />
              <span>Guardar</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
