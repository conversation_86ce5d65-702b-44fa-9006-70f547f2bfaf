'use client';

import { useEffect, useRef } from 'react';

interface AdSenseAdProps {
  adSlot: string;
  adFormat?: 'auto' | 'rectangle' | 'vertical' | 'horizontal';
  adLayout?: string;
  adLayoutKey?: string;
  style?: React.CSSProperties;
  className?: string;
  responsive?: boolean;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export default function AdSenseAd({
  adSlot,
  adFormat = 'auto',
  adLayout,
  adLayoutKey,
  style = { display: 'block' },
  className = '',
  responsive = true
}: AdSenseAdProps) {
  const adRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    try {
      // Verificar si AdSense está disponible
      if (typeof window !== 'undefined' && window.adsbygoogle) {
        // Inicializar el anuncio
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      }
    } catch (error) {
      console.error('Error loading AdSense ad:', error);
    }
  }, []);

  // No mostrar anuncios en desarrollo
  if (process.env.NODE_ENV === 'development') {
    return (
      <div 
        className={`bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-4 text-center ${className}`}
        style={style}
      >
        <div className="text-gray-500 text-sm">
          <div className="font-medium mb-1">Espacio publicitario</div>
          <div className="text-xs">AdSense Slot: {adSlot}</div>
          <div className="text-xs">Formato: {adFormat}</div>
        </div>
      </div>
    );
  }

  return (
    <div ref={adRef} className={className}>
      <ins
        className="adsbygoogle"
        style={style}
        data-ad-client={process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID}
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-ad-layout={adLayout}
        data-ad-layout-key={adLayoutKey}
        data-full-width-responsive={responsive ? 'true' : 'false'}
      />
    </div>
  );
}

// Componente para anuncio en el header
export function HeaderAd() {
  return (
    <AdSenseAd
      adSlot="1234567890" // Reemplazar con tu slot real
      adFormat="horizontal"
      className="w-full max-w-4xl mx-auto my-4"
      style={{ display: 'block', height: '90px' }}
    />
  );
}

// Componente para anuncio en la sidebar
export function SidebarAd() {
  return (
    <AdSenseAd
      adSlot="1234567891" // Reemplazar con tu slot real
      adFormat="rectangle"
      className="w-full"
      style={{ display: 'block', width: '300px', height: '250px' }}
    />
  );
}

// Componente para anuncio en el contenido
export function ContentAd() {
  return (
    <AdSenseAd
      adSlot="1234567892" // Reemplazar con tu slot real
      adFormat="auto"
      className="w-full my-8"
      style={{ display: 'block' }}
    />
  );
}

// Componente para anuncio en el footer
export function FooterAd() {
  return (
    <AdSenseAd
      adSlot="1234567893" // Reemplazar con tu slot real
      adFormat="horizontal"
      className="w-full max-w-6xl mx-auto"
      style={{ display: 'block', height: '90px' }}
    />
  );
}

// Componente para anuncio móvil
export function MobileAd() {
  return (
    <div className="block md:hidden">
      <AdSenseAd
        adSlot="1234567894" // Reemplazar con tu slot real
        adFormat="auto"
        className="w-full my-4"
        style={{ display: 'block' }}
      />
    </div>
  );
}

// Componente para anuncio responsivo
export function ResponsiveAd({ className = '' }: { className?: string }) {
  return (
    <AdSenseAd
      adSlot="1234567895" // Reemplazar con tu slot real
      adFormat="auto"
      className={`w-full ${className}`}
      style={{ display: 'block' }}
      responsive={true}
    />
  );
}
