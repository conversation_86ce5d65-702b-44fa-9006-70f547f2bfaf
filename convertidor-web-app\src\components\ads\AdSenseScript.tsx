'use client';

import Script from 'next/script';

interface AdSenseScriptProps {
  clientId?: string;
}

export default function AdSenseScript({ 
  clientId = process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID 
}: AdSenseScriptProps) {
  // No cargar AdSense en desarrollo
  if (process.env.NODE_ENV === 'development' || !clientId) {
    return null;
  }

  return (
    <>
      {/* Script principal de AdSense */}
      <Script
        async
        src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${clientId}`}
        crossOrigin="anonymous"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('AdSense script loaded successfully');
        }}
        onError={(e) => {
          console.error('Error loading AdSense script:', e);
        }}
      />

      {/* Script de inicialización */}
      <Script
        id="adsense-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.adsbygoogle = window.adsbygoogle || [];
            
            // Función para manejar errores de AdSense
            window.addEventListener('error', function(e) {
              if (e.target && e.target.src && e.target.src.includes('googlesyndication.com')) {
                console.warn('AdSense ad failed to load:', e.target.src);
              }
            });
            
            // Configuración global de AdSense
            (adsbygoogle = window.adsbygoogle || []).push({
              google_ad_client: "${clientId}",
              enable_page_level_ads: true
            });
          `
        }}
      />
    </>
  );
}

// Hook para verificar si AdSense está disponible
export function useAdSenseStatus() {
  const isAdSenseAvailable = () => {
    if (typeof window === 'undefined') return false;
    return !!(window.adsbygoogle && Array.isArray(window.adsbygoogle));
  };

  const isAdBlockerDetected = () => {
    if (typeof window === 'undefined') return false;
    
    // Método simple para detectar bloqueadores de anuncios
    const testAd = document.createElement('div');
    testAd.innerHTML = '&nbsp;';
    testAd.className = 'adsbox';
    testAd.style.position = 'absolute';
    testAd.style.left = '-10000px';
    document.body.appendChild(testAd);
    
    const isBlocked = testAd.offsetHeight === 0;
    document.body.removeChild(testAd);
    
    return isBlocked;
  };

  return {
    isAdSenseAvailable: isAdSenseAvailable(),
    isAdBlockerDetected: isAdBlockerDetected()
  };
}
