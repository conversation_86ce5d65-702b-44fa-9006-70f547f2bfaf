'use client';

import { useState, useEffect, useCallback } from 'react';
import { ArrowUpDown, RefreshCw, TrendingUp, Clock } from 'lucide-react';
import { currencies } from '@/data/conversions';
import { currencyService } from '@/services/currencyService';
import { useStore } from '@/store/useStore';
import { formatNumber, isValidNumber, parseNumber, debounce } from '@/lib/utils';
import { Currency, ExchangeRate, CurrencyConversion } from '@/types';

export default function CurrencyConverter() {
  const [fromCurrency, setFromCurrency] = useState<Currency>(currencies[0]); // USD
  const [toCurrency, setToCurrency] = useState<Currency>(currencies[1]); // EUR
  const [fromAmount, setFromAmount] = useState<string>('1');
  const [toAmount, setToAmount] = useState<string>('0');
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const { exchangeRates, setExchangeRates, addConversion } = useStore();

  // Función para obtener tasas de cambio
  const fetchExchangeRates = useCallback(async () => {
    setIsLoading(true);
    try {
      const rates = await currencyService.getExchangeRates('USD');
      setExchangeRates(rates);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching exchange rates:', error);
    } finally {
      setIsLoading(false);
    }
  }, [setExchangeRates]);

  // Función para convertir monedas
  const convertCurrency = useCallback((
    amount: string,
    from: Currency,
    to: Currency,
    rates: ExchangeRate | null
  ): string => {
    if (!rates || !isValidNumber(amount)) {
      return '0';
    }

    const numAmount = parseNumber(amount);
    const convertedAmount = currencyService.convertCurrency(
      numAmount,
      from.code,
      to.code,
      rates
    );

    return formatNumber(convertedAmount, 4);
  }, []);

  // Función debounced para actualizar la conversión
  const debouncedConvert = useCallback(
    debounce((amount: string) => {
      if (exchangeRates) {
        const result = convertCurrency(amount, fromCurrency, toCurrency, exchangeRates);
        setToAmount(result);
        
        // Agregar al historial si es una conversión válida
        if (isValidNumber(amount) && parseNumber(amount) > 0) {
          const conversion: CurrencyConversion = {
            fromCurrency,
            toCurrency,
            fromAmount: parseNumber(amount),
            toAmount: parseNumber(result),
            rate: currencyService.convertCurrency(1, fromCurrency.code, toCurrency.code, exchangeRates),
            timestamp: new Date(),
          };
          
          // Agregar al historial general (adaptado para el tipo ConversionResult)
          addConversion({
            fromValue: conversion.fromAmount,
            fromUnit: { id: fromCurrency.code, name: fromCurrency.name, symbol: fromCurrency.symbol, factor: 1 },
            toValue: conversion.toAmount,
            toUnit: { id: toCurrency.code, name: toCurrency.name, symbol: toCurrency.symbol, factor: 1 },
            category: 'currency',
            timestamp: conversion.timestamp,
          });
        }
      }
    }, 300),
    [fromCurrency, toCurrency, exchangeRates, convertCurrency, addConversion]
  );

  // Efecto para cargar tasas de cambio al montar el componente
  useEffect(() => {
    if (!exchangeRates) {
      fetchExchangeRates();
    }
  }, [exchangeRates, fetchExchangeRates]);

  // Efecto para actualizar la conversión cuando cambian los valores
  useEffect(() => {
    debouncedConvert(fromAmount);
  }, [fromAmount, fromCurrency, toCurrency, debouncedConvert]);

  // Función para intercambiar monedas
  const swapCurrencies = () => {
    setFromCurrency(toCurrency);
    setToCurrency(fromCurrency);
    setFromAmount(toAmount);
  };

  // Función para manejar cambio en el monto
  const handleAmountChange = (value: string) => {
    // Permitir solo números y punto decimal
    const sanitized = value.replace(/[^0-9.]/g, '');
    setFromAmount(sanitized);
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <TrendingUp className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Convertidor de Monedas</h2>
            <p className="text-sm text-gray-500">Tasas de cambio en tiempo real</p>
          </div>
        </div>
        
        <button
          onClick={fetchExchangeRates}
          disabled={isLoading}
          className="flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>Actualizar</span>
        </button>
      </div>

      {/* Convertidor */}
      <div className="space-y-4">
        {/* Moneda de origen */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Desde</label>
          <div className="flex space-x-3">
            <select
              value={fromCurrency.code}
              onChange={(e) => {
                const currency = currencies.find(c => c.code === e.target.value);
                if (currency) setFromCurrency(currency);
              }}
              className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {currencies.map((currency) => (
                <option key={currency.code} value={currency.code}>
                  {currency.flag} {currency.code} - {currency.name}
                </option>
              ))}
            </select>
            <input
              type="text"
              value={fromAmount}
              onChange={(e) => handleAmountChange(e.target.value)}
              placeholder="0.00"
              className="w-32 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
            />
          </div>
        </div>

        {/* Botón de intercambio */}
        <div className="flex justify-center">
          <button
            onClick={swapCurrencies}
            className="p-2 bg-blue-50 hover:bg-blue-100 rounded-full transition-colors"
          >
            <ArrowUpDown className="w-5 h-5 text-blue-600" />
          </button>
        </div>

        {/* Moneda de destino */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Hacia</label>
          <div className="flex space-x-3">
            <select
              value={toCurrency.code}
              onChange={(e) => {
                const currency = currencies.find(c => c.code === e.target.value);
                if (currency) setToCurrency(currency);
              }}
              className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {currencies.map((currency) => (
                <option key={currency.code} value={currency.code}>
                  {currency.flag} {currency.code} - {currency.name}
                </option>
              ))}
            </select>
            <div className="w-32 px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-right font-mono">
              {toAmount}
            </div>
          </div>
        </div>
      </div>

      {/* Información adicional */}
      {exchangeRates && lastUpdated && (
        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>Actualizado: {lastUpdated.toLocaleTimeString()}</span>
            </div>
            <div>
              1 {fromCurrency.code} = {formatNumber(
                currencyService.convertCurrency(1, fromCurrency.code, toCurrency.code, exchangeRates),
                4
              )} {toCurrency.code}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
