'use client';

import { useState, useEffect, useCallback } from 'react';
import { ArrowUpDown, Calculator, Copy, Check } from 'lucide-react';
import { conversionCategories } from '@/data/conversions';
import { conversionService } from '@/services/conversionService';
import { useStore } from '@/store/useStore';
import { formatNumber, isValidNumber, parseNumber, debounce } from '@/lib/utils';
import { ConversionCategory, ConversionUnit } from '@/types';

interface UniversalConverterProps {
  categoryId: string;
}

export default function UniversalConverter({ categoryId }: UniversalConverterProps) {
  const category = conversionCategories.find(cat => cat.id === categoryId);
  
  if (!category) {
    return <div>Categoría no encontrada</div>;
  }

  const [fromUnit, setFromUnit] = useState<ConversionUnit>(category.units[0]);
  const [toUnit, setToUnit] = useState<ConversionUnit>(category.units[1] || category.units[0]);
  const [fromValue, setFromValue] = useState<string>('1');
  const [toValue, setToValue] = useState<string>('0');
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [allConversions, setAllConversions] = useState<Array<{ unit: ConversionUnit; value: number }>>([]);

  const { addConversion } = useStore();

  // Función para realizar la conversión
  const performConversion = useCallback((
    value: string,
    from: ConversionUnit,
    to: ConversionUnit,
    cat: ConversionCategory
  ): string => {
    if (!isValidNumber(value)) {
      return '0';
    }

    const numValue = parseNumber(value);
    const result = conversionService.convert(numValue, from, to, cat);
    return formatNumber(result, 6);
  }, []);

  // Función debounced para actualizar conversiones
  const debouncedConvert = useCallback(
    debounce((value: string) => {
      if (isValidNumber(value)) {
        const numValue = parseNumber(value);
        
        // Conversión principal
        const mainResult = performConversion(value, fromUnit, toUnit, category);
        setToValue(mainResult);

        // Todas las conversiones
        const allResults = conversionService.convertToAll(numValue, fromUnit, category);
        setAllConversions(allResults);

        // Agregar al historial
        if (numValue > 0) {
          addConversion({
            fromValue: numValue,
            fromUnit: fromUnit,
            toValue: parseNumber(mainResult),
            toUnit: toUnit,
            category: category.id,
            timestamp: new Date(),
          });
        }
      } else {
        setToValue('0');
        setAllConversions([]);
      }
    }, 300),
    [fromUnit, toUnit, category, performConversion, addConversion]
  );

  // Efecto para actualizar conversiones cuando cambian los valores
  useEffect(() => {
    debouncedConvert(fromValue);
  }, [fromValue, fromUnit, toUnit, debouncedConvert]);

  // Función para intercambiar unidades
  const swapUnits = () => {
    setFromUnit(toUnit);
    setToUnit(fromUnit);
    setFromValue(toValue);
  };

  // Función para manejar cambio en el valor
  const handleValueChange = (value: string) => {
    // Permitir números, punto decimal y signo negativo para temperatura
    const regex = category.id === 'temperature' ? /[^0-9.-]/g : /[^0-9.]/g;
    const sanitized = value.replace(regex, '');
    setFromValue(sanitized);
  };

  // Función para copiar al portapapeles
  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Error copying to clipboard:', err);
    }
  };

  return (
    <div className="space-y-6">
      {/* Convertidor principal */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Calculator className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">{category.name}</h2>
            <p className="text-sm text-gray-500">{category.description}</p>
          </div>
        </div>

        <div className="space-y-4">
          {/* Unidad de origen */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Desde</label>
            <div className="flex space-x-3">
              <select
                value={fromUnit.id}
                onChange={(e) => {
                  const unit = category.units.find(u => u.id === e.target.value);
                  if (unit) setFromUnit(unit);
                }}
                className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {category.units.map((unit) => (
                  <option key={unit.id} value={unit.id}>
                    {unit.name} ({unit.symbol})
                  </option>
                ))}
              </select>
              <input
                type="text"
                value={fromValue}
                onChange={(e) => handleValueChange(e.target.value)}
                placeholder="0"
                className="w-32 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
              />
            </div>
          </div>

          {/* Botón de intercambio */}
          <div className="flex justify-center">
            <button
              onClick={swapUnits}
              className="p-2 bg-blue-50 hover:bg-blue-100 rounded-full transition-colors"
            >
              <ArrowUpDown className="w-5 h-5 text-blue-600" />
            </button>
          </div>

          {/* Unidad de destino */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Hacia</label>
            <div className="flex space-x-3">
              <select
                value={toUnit.id}
                onChange={(e) => {
                  const unit = category.units.find(u => u.id === e.target.value);
                  if (unit) setToUnit(unit);
                }}
                className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {category.units.map((unit) => (
                  <option key={unit.id} value={unit.id}>
                    {unit.name} ({unit.symbol})
                  </option>
                ))}
              </select>
              <div className="w-32 px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-right font-mono">
                {toValue}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Todas las conversiones */}
      {allConversions.length > 0 && (
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {fromValue} {fromUnit.symbol} equivale a:
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {allConversions.map((conversion, index) => (
              <div
                key={conversion.unit.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex-1">
                  <div className="font-medium text-gray-900">
                    {formatNumber(conversion.value, 4)} {conversion.unit.symbol}
                  </div>
                  <div className="text-sm text-gray-500">{conversion.unit.name}</div>
                </div>
                <button
                  onClick={() => copyToClipboard(formatNumber(conversion.value, 4), index)}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Copiar"
                >
                  {copiedIndex === index ? (
                    <Check className="w-4 h-4 text-green-500" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
