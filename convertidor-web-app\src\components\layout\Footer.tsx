'use client';

import { <PERSON><PERSON><PERSON>, Heart } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo y descripción */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <Calculator className="h-6 w-6 text-blue-600" />
              <span className="text-lg font-bold text-gray-900">Convertidor Universal</span>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Herramientas de conversión gratuitas y fáciles de usar para estudiantes. 
              Convierte monedas, medidas, temperatura y mucho más con precisión y rapidez.
            </p>
            <div className="flex items-center text-sm text-gray-500">
              <span>Hecho con</span>
              <Heart className="h-4 w-4 mx-1 text-red-500 fill-current" />
              <span>para estudiantes</span>
            </div>
          </div>

          {/* Enlaces útiles */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Convertidores</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li><a href="#monedas" className="hover:text-blue-600 transition-colors">Monedas</a></li>
              <li><a href="#longitud" className="hover:text-blue-600 transition-colors">Longitud</a></li>
              <li><a href="#peso" className="hover:text-blue-600 transition-colors">Peso</a></li>
              <li><a href="#temperatura" className="hover:text-blue-600 transition-colors">Temperatura</a></li>
              <li><a href="#volumen" className="hover:text-blue-600 transition-colors">Volumen</a></li>
            </ul>
          </div>

          {/* Información */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Información</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li><a href="#sobre-nosotros" className="hover:text-blue-600 transition-colors">Sobre nosotros</a></li>
              <li><a href="#privacidad" className="hover:text-blue-600 transition-colors">Privacidad</a></li>
              <li><a href="#terminos" className="hover:text-blue-600 transition-colors">Términos</a></li>
              <li><a href="#contacto" className="hover:text-blue-600 transition-colors">Contacto</a></li>
            </ul>
          </div>
        </div>

        {/* Línea divisoria y copyright */}
        <div className="border-t mt-8 pt-8 text-center text-sm text-gray-500">
          <p>&copy; {currentYear} Convertidor Universal. Todos los derechos reservados.</p>
          <p className="mt-1">Diseñado para facilitar el aprendizaje de los estudiantes.</p>
        </div>
      </div>
    </footer>
  );
}
