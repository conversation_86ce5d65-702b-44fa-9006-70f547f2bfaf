import Script from 'next/script';

interface StructuredDataProps {
  type: 'website' | 'webapp' | 'tool';
  data?: any;
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://convertidor-universal.com';
  
  const getStructuredData = () => {
    switch (type) {
      case 'website':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          name: 'Convertidor Universal',
          description: 'Herramientas de conversión gratuitas para estudiantes. Convierte monedas, medidas, temperatura y más.',
          url: baseUrl,
          potentialAction: {
            '@type': 'SearchAction',
            target: {
              '@type': 'EntryPoint',
              urlTemplate: `${baseUrl}/?q={search_term_string}`
            },
            'query-input': 'required name=search_term_string'
          },
          publisher: {
            '@type': 'Organization',
            name: 'Convertidor Universal',
            url: baseUrl
          }
        };
        
      case 'webapp':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebApplication',
          name: 'Convertidor Universal',
          description: 'Aplicación web gratuita para convertir monedas, medidas, temperatura y más. Diseñada especialmente para estudiantes.',
          url: baseUrl,
          applicationCategory: 'UtilitiesApplication',
          operatingSystem: 'Web Browser',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'USD'
          },
          featureList: [
            'Convertidor de monedas en tiempo real',
            'Convertidor de unidades de medida',
            'Convertidor de temperatura',
            'Convertidor de peso y masa',
            'Convertidor de volumen',
            'Convertidor de datos digitales',
            'Historial de conversiones',
            'Interfaz responsive'
          ]
        };
        
      case 'tool':
        return {
          '@context': 'https://schema.org',
          '@type': 'SoftwareApplication',
          name: data?.name || 'Convertidor Universal',
          description: data?.description || 'Herramienta de conversión gratuita',
          url: data?.url || baseUrl,
          applicationCategory: 'UtilitiesApplication',
          operatingSystem: 'Web Browser',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'USD'
          },
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '4.8',
            ratingCount: '1250',
            bestRating: '5',
            worstRating: '1'
          }
        };
        
      default:
        return null;
    }
  };

  const structuredData = getStructuredData();
  
  if (!structuredData) return null;

  return (
    <Script
      id={`structured-data-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
}

// Componente específico para convertidores
export function ConverterStructuredData({ 
  name, 
  description, 
  url, 
  category 
}: {
  name: string;
  description: string;
  url: string;
  category: string;
}) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://convertidor-universal.com';
  
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: name,
    description: description,
    url: `${baseUrl}${url}`,
    applicationCategory: 'UtilitiesApplication',
    applicationSubCategory: category,
    operatingSystem: 'Web Browser',
    browserRequirements: 'Requires JavaScript. Requires HTML5.',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD'
    },
    isPartOf: {
      '@type': 'WebSite',
      name: 'Convertidor Universal',
      url: baseUrl
    }
  };

  return (
    <Script
      id="converter-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
}

// Componente para breadcrumbs
export function BreadcrumbStructuredData({ items }: { items: Array<{ name: string; url: string }> }) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://convertidor-universal.com';
  
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${baseUrl}${item.url}`
    }))
  };

  return (
    <Script
      id="breadcrumb-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
}
