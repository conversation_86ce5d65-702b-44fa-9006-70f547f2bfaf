import { ConversionUnit, ConversionCategory } from '@/types';

class ConversionService {
  // Conversión estándar usando factores
  convertStandard(
    value: number,
    fromUnit: ConversionUnit,
    toUnit: ConversionUnit
  ): number {
    if (fromUnit.id === toUnit.id) {
      return value;
    }

    // Convertir a unidad base y luego a unidad destino
    const baseValue = value * fromUnit.factor;
    return baseValue / toUnit.factor;
  }

  // Conversión especial para temperatura
  convertTemperature(
    value: number,
    fromUnit: ConversionUnit,
    toUnit: ConversionUnit
  ): number {
    if (fromUnit.id === toUnit.id) {
      return value;
    }

    let celsius: number;

    // Convertir a Celsius primero
    switch (fromUnit.id) {
      case 'celsius':
        celsius = value;
        break;
      case 'fahrenheit':
        celsius = (value - 32) * 5 / 9;
        break;
      case 'kelvin':
        celsius = value - 273.15;
        break;
      default:
        celsius = value;
    }

    // Convertir de Celsius a la unidad destino
    switch (toUnit.id) {
      case 'celsius':
        return celsius;
      case 'fahrenheit':
        return (celsius * 9 / 5) + 32;
      case 'kelvin':
        return celsius + 273.15;
      default:
        return celsius;
    }
  }

  // Función principal de conversión
  convert(
    value: number,
    fromUnit: ConversionUnit,
    toUnit: ConversionUnit,
    category: ConversionCategory
  ): number {
    if (category.id === 'temperature') {
      return this.convertTemperature(value, fromUnit, toUnit);
    }

    return this.convertStandard(value, fromUnit, toUnit);
  }

  // Obtener todas las conversiones posibles para un valor
  convertToAll(
    value: number,
    fromUnit: ConversionUnit,
    category: ConversionCategory
  ): Array<{ unit: ConversionUnit; value: number }> {
    return category.units
      .filter(unit => unit.id !== fromUnit.id)
      .map(unit => ({
        unit,
        value: this.convert(value, fromUnit, unit, category)
      }));
  }

  // Buscar unidad por ID en una categoría
  findUnit(unitId: string, category: ConversionCategory): ConversionUnit | null {
    return category.units.find(unit => unit.id === unitId) || null;
  }

  // Obtener unidad base de una categoría
  getBaseUnit(category: ConversionCategory): ConversionUnit | null {
    return this.findUnit(category.baseUnit, category);
  }

  // Validar si una conversión es posible
  canConvert(
    fromUnit: ConversionUnit,
    toUnit: ConversionUnit,
    category: ConversionCategory
  ): boolean {
    const fromExists = category.units.some(unit => unit.id === fromUnit.id);
    const toExists = category.units.some(unit => unit.id === toUnit.id);
    return fromExists && toExists;
  }

  // Obtener conversiones comunes para una categoría
  getCommonConversions(category: ConversionCategory): Array<{
    from: ConversionUnit;
    to: ConversionUnit;
    example: number;
  }> {
    const common: Array<{ from: string; to: string; example: number }> = [];

    switch (category.id) {
      case 'length':
        common.push(
          { from: 'meter', to: 'foot', example: 1 },
          { from: 'kilometer', to: 'mile', example: 1 },
          { from: 'inch', to: 'centimeter', example: 1 }
        );
        break;
      case 'weight':
        common.push(
          { from: 'kilogram', to: 'pound', example: 1 },
          { from: 'gram', to: 'ounce', example: 100 },
          { from: 'pound', to: 'kilogram', example: 1 }
        );
        break;
      case 'temperature':
        common.push(
          { from: 'celsius', to: 'fahrenheit', example: 0 },
          { from: 'fahrenheit', to: 'celsius', example: 32 },
          { from: 'celsius', to: 'kelvin', example: 0 }
        );
        break;
      case 'volume':
        common.push(
          { from: 'liter', to: 'gallon_us', example: 1 },
          { from: 'milliliter', to: 'fluid_ounce_us', example: 100 },
          { from: 'cubic_meter', to: 'liter', example: 1 }
        );
        break;
      case 'area':
        common.push(
          { from: 'square_meter', to: 'square_foot', example: 1 },
          { from: 'hectare', to: 'acre', example: 1 },
          { from: 'square_kilometer', to: 'square_mile', example: 1 }
        );
        break;
      case 'speed':
        common.push(
          { from: 'kilometer_per_hour', to: 'mile_per_hour', example: 100 },
          { from: 'meter_per_second', to: 'kilometer_per_hour', example: 10 },
          { from: 'knot', to: 'kilometer_per_hour', example: 10 }
        );
        break;
      case 'digital':
        common.push(
          { from: 'gigabyte', to: 'megabyte', example: 1 },
          { from: 'megabyte', to: 'kilobyte', example: 1 },
          { from: 'byte', to: 'bit', example: 1 }
        );
        break;
      default:
        break;
    }

    return common
      .map(({ from, to, example }) => {
        const fromUnit = this.findUnit(from, category);
        const toUnit = this.findUnit(to, category);
        if (fromUnit && toUnit) {
          return { from: fromUnit, to: toUnit, example };
        }
        return null;
      })
      .filter((item): item is NonNullable<typeof item> => item !== null);
  }
}

export const conversionService = new ConversionService();
