import axios from 'axios';
import { ExchangeRate, Currency } from '@/types';

// API gratuita para tasas de cambio - ExchangeRate-API
const BASE_URL = 'https://api.exchangerate-api.com/v4/latest';

// API alternativa gratuita - Fixer.io (requiere registro pero tiene plan gratuito)
// const FIXER_BASE_URL = 'https://api.fixer.io/latest';

// Para desarrollo, usaremos datos mock si no hay conexión
const MOCK_RATES = {
  USD: 1,
  EUR: 0.85,
  GBP: 0.73,
  JPY: 110.0,
  CAD: 1.25,
  AUD: 1.35,
  MXN: 20.5,
  CRC: 620.0,
  GTQ: 7.8,
  HNL: 24.5,
  NIO: 36.2,
  PAB: 1.0,
  DOP: 58.5,
  COP: 4200.0,
  PEN: 3.8,
  CLP: 800.0,
  ARS: 350.0,
  BRL: 5.2,
};

class CurrencyService {
  private cache: Map<string, { data: ExchangeRate; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutos

  async getExchangeRates(baseCurrency: string = 'USD'): Promise<ExchangeRate> {
    const cacheKey = baseCurrency;
    const cached = this.cache.get(cacheKey);
    
    // Verificar si tenemos datos en caché válidos
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }

    try {
      const response = await axios.get(`${BASE_URL}/${baseCurrency}`, {
        timeout: 5000,
      });

      const exchangeRate: ExchangeRate = {
        base: response.data.base,
        rates: response.data.rates,
        lastUpdated: new Date(response.data.date),
      };

      // Guardar en caché
      this.cache.set(cacheKey, {
        data: exchangeRate,
        timestamp: Date.now(),
      });

      return exchangeRate;
    } catch (error) {
      console.warn('Error fetching exchange rates, using mock data:', error);
      
      // Usar datos mock como fallback
      const mockExchangeRate: ExchangeRate = {
        base: baseCurrency,
        rates: this.adjustRatesForBase(MOCK_RATES, baseCurrency),
        lastUpdated: new Date(),
      };

      return mockExchangeRate;
    }
  }

  private adjustRatesForBase(rates: Record<string, number>, baseCurrency: string): Record<string, number> {
    const baseRate = rates[baseCurrency] || 1;
    const adjustedRates: Record<string, number> = {};

    for (const [currency, rate] of Object.entries(rates)) {
      adjustedRates[currency] = rate / baseRate;
    }

    return adjustedRates;
  }

  convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
    rates: ExchangeRate
  ): number {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    // Si la moneda base es la moneda de origen
    if (rates.base === fromCurrency) {
      return amount * (rates.rates[toCurrency] || 1);
    }

    // Si la moneda base es la moneda de destino
    if (rates.base === toCurrency) {
      return amount / (rates.rates[fromCurrency] || 1);
    }

    // Conversión a través de la moneda base
    const amountInBase = amount / (rates.rates[fromCurrency] || 1);
    return amountInBase * (rates.rates[toCurrency] || 1);
  }

  async getPopularCurrencies(): Promise<Currency[]> {
    // Retornar las monedas más populares
    return [
      { code: 'USD', name: 'Dólar estadounidense', symbol: '$', flag: '🇺🇸' },
      { code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺' },
      { code: 'GBP', name: 'Libra esterlina', symbol: '£', flag: '🇬🇧' },
      { code: 'JPY', name: 'Yen japonés', symbol: '¥', flag: '🇯🇵' },
      { code: 'CAD', name: 'Dólar canadiense', symbol: 'C$', flag: '🇨🇦' },
      { code: 'AUD', name: 'Dólar australiano', symbol: 'A$', flag: '🇦🇺' },
      { code: 'MXN', name: 'Peso mexicano', symbol: '$', flag: '🇲🇽' },
      { code: 'BRL', name: 'Real brasileño', symbol: 'R$', flag: '🇧🇷' },
    ];
  }

  clearCache(): void {
    this.cache.clear();
  }
}

export const currencyService = new CurrencyService();
