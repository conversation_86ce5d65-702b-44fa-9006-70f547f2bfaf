import { SearchResult } from '@/types';
import { conversionCategories } from '@/data/conversions';
import { currencies } from '@/data/conversions';

class SearchService {
  private searchIndex: SearchResult[] = [];

  constructor() {
    this.buildSearchIndex();
  }

  private buildSearchIndex() {
    // Agregar convertidores de categorías
    this.searchIndex = conversionCategories.map(category => ({
      id: category.id,
      title: `Convertidor de ${category.name}`,
      description: category.description,
      category: 'Convertidor',
      path: `/${this.getPageSlug(category.id)}`,
      keywords: [
        category.name.toLowerCase(),
        ...category.units.map(unit => unit.name.toLowerCase()),
        ...category.units.map(unit => unit.symbol.toLowerCase()),
        'convertir',
        'conversión',
        'calculadora'
      ]
    }));

    // Agregar convertidor de monedas
    this.searchIndex.push({
      id: 'currency',
      title: 'Convertido<PERSON> de Monedas',
      description: 'Convierte entre más de 18 monedas internacionales con tasas en tiempo real',
      category: 'Convertidor',
      path: '/monedas',
      keywords: [
        'monedas',
        'divisas',
        'cambio',
        'dólar',
        'euro',
        'peso',
        'real',
        'libra',
        'yen',
        ...currencies.map(currency => currency.name.toLowerCase()),
        ...currencies.map(currency => currency.code.toLowerCase()),
        'tasa de cambio',
        'convertir monedas'
      ]
    });

    // Agregar búsquedas específicas comunes
    const commonSearches = [
      {
        id: 'metros-pies',
        title: 'Convertir metros a pies',
        description: 'Conversión rápida de metros a pies',
        category: 'Conversión específica',
        path: '/longitud',
        keywords: ['metros a pies', 'metros pies', 'm a ft', 'meter to feet']
      },
      {
        id: 'celsius-fahrenheit',
        title: 'Convertir Celsius a Fahrenheit',
        description: 'Conversión de temperatura Celsius a Fahrenheit',
        category: 'Conversión específica',
        path: '/temperatura',
        keywords: ['celsius fahrenheit', 'c a f', '°c a °f', 'grados celsius fahrenheit']
      },
      {
        id: 'kg-libras',
        title: 'Convertir kilogramos a libras',
        description: 'Conversión de peso kilogramos a libras',
        category: 'Conversión específica',
        path: '/peso',
        keywords: ['kilogramos libras', 'kg a lb', 'kilos libras', 'peso']
      },
      {
        id: 'litros-galones',
        title: 'Convertir litros a galones',
        description: 'Conversión de volumen litros a galones',
        category: 'Conversión específica',
        path: '/volumen',
        keywords: ['litros galones', 'l a gal', 'volumen']
      },
      {
        id: 'usd-eur',
        title: 'Convertir USD a EUR',
        description: 'Conversión de dólares a euros',
        category: 'Conversión específica',
        path: '/monedas',
        keywords: ['usd eur', 'dolar euro', 'dolares euros', 'usd a eur']
      },
      {
        id: 'gb-mb',
        title: 'Convertir GB a MB',
        description: 'Conversión de gigabytes a megabytes',
        category: 'Conversión específica',
        path: '/datos-digitales',
        keywords: ['gb mb', 'gigabytes megabytes', 'gb a mb', 'almacenamiento']
      }
    ];

    this.searchIndex.push(...commonSearches);
  }

  private getPageSlug(categoryId: string): string {
    const slugMap: Record<string, string> = {
      'length': 'longitud',
      'area': 'superficie',
      'volume': 'volumen',
      'weight': 'peso',
      'temperature': 'temperatura',
      'time': 'tiempo',
      'speed': 'velocidad',
      'digital': 'datos-digitales',
      'energy': 'energia',
      'pressure': 'presion',
    };
    return slugMap[categoryId] || categoryId;
  }

  search(query: string, limit: number = 10): SearchResult[] {
    if (!query || query.trim().length < 2) {
      return [];
    }

    const normalizedQuery = query.toLowerCase().trim();
    const words = normalizedQuery.split(/\s+/);

    const results = this.searchIndex
      .map(item => {
        let score = 0;

        // Búsqueda exacta en título (puntuación alta)
        if (item.title.toLowerCase().includes(normalizedQuery)) {
          score += 100;
        }

        // Búsqueda exacta en descripción
        if (item.description.toLowerCase().includes(normalizedQuery)) {
          score += 50;
        }

        // Búsqueda en palabras clave
        const keywordMatches = item.keywords.filter(keyword => 
          keyword.includes(normalizedQuery) || 
          words.some(word => keyword.includes(word))
        );
        score += keywordMatches.length * 20;

        // Búsqueda por palabras individuales en título
        words.forEach(word => {
          if (word.length > 2 && item.title.toLowerCase().includes(word)) {
            score += 30;
          }
        });

        // Búsqueda por palabras individuales en descripción
        words.forEach(word => {
          if (word.length > 2 && item.description.toLowerCase().includes(word)) {
            score += 15;
          }
        });

        return { ...item, score };
      })
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    return results.map(({ score, ...item }) => item);
  }

  getPopularSearches(): SearchResult[] {
    return [
      this.searchIndex.find(item => item.id === 'currency')!,
      this.searchIndex.find(item => item.id === 'length')!,
      this.searchIndex.find(item => item.id === 'weight')!,
      this.searchIndex.find(item => item.id === 'temperature')!,
      this.searchIndex.find(item => item.id === 'volume')!,
    ].filter(Boolean);
  }

  getAllCategories(): string[] {
    return [...new Set(this.searchIndex.map(item => item.category))];
  }

  searchByCategory(category: string): SearchResult[] {
    return this.searchIndex.filter(item => item.category === category);
  }
}

export const searchService = new SearchService();
